stages:
  - build
  - deploy

variables:
  APPCMD_EXE:  'C:\Windows\System32\inetsrv\AppCmd.exe'
  REGISTRY_URL: "https://imagerepository.euroland.com"
  REGISTRY_HOST: "imagerepository.euroland.com"
  BACKEND_PROJECT_NAME: "customerinterface3.api"
  DOCKER_DRIVER: overlay2
  GIT_STRATEGY: clone
  DEPLOYMENT_SERVER: "my-deployment-server.example.com"

.frontend_deploy_template: &frontend_deploy_template
  stage: deploy
  script:
    - cd clientapp
    - npm install
    - npm run build "--" --mode $env:DEPLOY_ENV
    - |
      $deployParams = @{
          source    = Join-Path $PWD "dist"
          siteName   = "$env:SITE_NAME"
          appPath    = "$env:APP_PATH"
          user       = "$DEPLOY_USER"
          passwd     = "$DEPLOY_PWD"
          server     = "$env:DEPLOY_SERVER1"
          port       = $env:DEPLOY_SERVER_PORT1
      }

      & ".\ms_deploy.ps1" @deployParams
     
      if($env:DEPLOY_SERVER2 -and $env:DEPLOY_SERVER_PORT2) {
        $deployParams.server = $env:DEPLOY_SERVER2
        $deployParams.port = $env:DEPLOY_SERVER_PORT2

        & ".\ms_deploy.ps1" @deployParams
      }

      if ($LASTEXITCODE -ne 0) {
        Write-Host "Deployment failed with exit code $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
      }

build_merge_request:
  tags:
    - vietnam-buildtest-powershell
  stage: build
  only:
    refs:
      - merge_requests
    variables:
      - '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'
  script:
    - |
      cd server
      dotnet restore
      dotnet build --no-restore
      cd..
    - |
      cd clientapp
      npm install
      npm run build

# backend_build_qa:
#   tags:
#     - vietnam-buildtest-powershell
#   stage: build
#   script:
#     - cd backend
#     - dotnet restore
#     - dotnet build --no-restore
#   only:
#     refs:
#       - develop
#     changes:
#         - backend/src/*
# fontend_build_qa:
#   tags:
#     - vietnam-buildtest-powershell
#   stage: build
#   script:
#     - cd frontend
#     - npm install
#     - npm run build "--" --mode qa
#   only:
#     refs:
#       - develop
#     changes:
#       - frontend/src/*

backend_deploy_qa:
  tags:
    - vietnam-buildtest-powershell
  stage: deploy
  variables:
    DEPLOY_ENV: 'QA'
    PUBLISH_FOLDER: 'publish'
    APP_POOL_NAME: 'Tools-EurolandIdAPI'
    DEPLOY_SERVER: 'BINGO'
    SITE_NAME: 'tools site'
    APP_PATH: '/tools/EurolandId-Api'
    APP_NAME: 'EurolandId-Api'
  script:
    - cd server
    - |
      echo "Publishing .NET Core application..."
      dotnet restore
      dotnet publish --no-restore -c Release -p:PublishProfile=Production -p:EnvironmentName=$DEPLOY_ENV -p:MSDeployServiceURL="https://$($env:DEPLOY_SERVER):8172/msdeploy.axd" -p:DeployIisAppPath="$env:SITE_NAME$APP_PATH" -p:UserName="$env:DEV_VN_DEPLOY_USER" -p:Password="$env:DEV_VN_DEPLOY_PSW"
    - echo "Deployment completed successfully!"
  only:
    refs:
      - develop
    changes:
      - server/**/*
    
frontend_deploy_qa:
  <<: *frontend_deploy_template
  tags:
    - vietnam-buildtest-powershell
  variables:
    DEPLOY_ENV: qa
    DEPLOY_SERVER1: 'BINGO'
    DEPLOY_SERVER_PORT1: 8172
    SITE_NAME: 'tools site'
    APP_PATH: '/tools/user-preferences'
  before_script:
    - Set-Variable -Name "DEPLOY_USER" -Value "$env:DEV_VN_DEPLOY_USER"
    - Set-Variable -Name "DEPLOY_PWD" -Value "$env:DEV_VN_DEPLOY_PSW"
  only:
    refs:
      - develop
    changes:
      - clientapp/**/*