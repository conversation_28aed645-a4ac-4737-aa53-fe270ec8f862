{"name": "admin-shadcn-test", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build --base=/tools/user-preferences/", "lint": "eslint .", "preview": "vite preview "}, "dependencies": {"@euroland/libs": "^2.1.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@tanstack/react-query": "^5.76.1", "@urql/core": "^5.1.1", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "es-toolkit": "^1.32.0", "graphql": "^16.11.0", "lucide-react": "^0.474.0", "oidc-client-ts": "^3.1.0", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-markdown": "^9.0.3", "react-oidc-context": "^3.2.0", "react-router-dom": "^7.1.5", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "url-join": "^5.0.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^22.13.1", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}