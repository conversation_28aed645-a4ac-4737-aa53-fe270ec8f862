import { Routes, Route } from "react-router-dom";
import Home from "./pages/Home";
import Comments from "./pages/Comments";
import FollowedCompanies from "./pages/FollowedCompanies";
import ProfileSettings from "./pages/ProfileSettings";
import SearchHistory from "./pages/SearchHistory";
import LogoutCallback from "./pages/LogoutCallback";
import LoginCallback from "./pages/LoginCallback";
import { OidcAuthAppRoutes } from "./configs/oidcConfig";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AppLayout } from "./components/layout/AppLayout";

const queryClient = new QueryClient();

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Routes>
      <Route path="/" element={<AppLayout />}>
        <Route path="/" element={<Home />} />
        <Route path="/comments" element={<Comments />} />
        <Route path="/profile" element={<ProfileSettings />} />
        <Route path="/search-history" element={<SearchHistory />} />
        <Route path="/followed-companies" element={<FollowedCompanies />} />
      </Route>
      <Route
        path={OidcAuthAppRoutes.SIGNIN_CALLBACK}
        element={<LoginCallback />}
      />
      <Route
        path={OidcAuthAppRoutes.SIGNOUT_CALLBACK}
          element={<LogoutCallback />}
        />
      </Routes>
    </QueryClientProvider>
  );
}
