import { useEffect, useState } from "react";
import type { Company, PressRelease, CompanySubscriptionItem } from "@/types/dashboard";
import RecentUpdatesList from "./RecentUpdatesList";
import StockPerformance from "./StockPerformance";
import RecentUpdatesListSkeleton from "./RecentUpdatesListSkeleton";
import StockPerformanceSkeleton from "./StockPerformanceSkeleton";
import { useInvesterDashboard } from "@/services/graphql/hooks";
import { processCompanyPressReleases } from "@/lib/utils";

const CompanyDashboardSection = () => {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [recentUpdates, setRecentUpdates] = useState<PressRelease[]>([]);
  const { data: dashboardData, isLoading: isDashboardLoading } = useInvesterDashboard();

  useEffect(() => {
    if (dashboardData && !isDashboardLoading) {
      const profileData = dashboardData?.user?.userProfile?.companysubscriptions?.nodes;
      const companiesData = profileData?.map((item: CompanySubscriptionItem) => item.company) || [];
      setCompanies(companiesData);
      
      const processedReleases = processCompanyPressReleases(companiesData);
      setRecentUpdates(processedReleases);
    }
  }, [dashboardData, isDashboardLoading]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {isDashboardLoading ? (
        <RecentUpdatesListSkeleton />
      ) : (
        <RecentUpdatesList updates={recentUpdates} />
      )}
      {isDashboardLoading ? (
        <StockPerformanceSkeleton />
      ) : (
        <StockPerformance companies={companies} />
      )}
    </div>
  );
};

export default CompanyDashboardSection; 