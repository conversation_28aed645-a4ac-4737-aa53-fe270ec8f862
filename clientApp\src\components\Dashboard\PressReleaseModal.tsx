import * as Dialog from '@radix-ui/react-dialog';
import { X, Loader2 } from 'lucide-react';
import { usePressReleaseDetail } from '@/services/graphql/hooks';
import { Markdown } from '@/components/Markdown';
import { i18n } from '@euroland/libs';
import { Button } from '@/components/ui/button';

interface PressReleaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  pressReleaseId: string | null;
  title?: string;
}

export const PressReleaseModal = ({
  isOpen,
  onClose,
  pressReleaseId,
  title,
}: PressReleaseModalProps) => {
  const { data, isLoading, error } = usePressReleaseDetail(pressReleaseId);

  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <Dialog.Content className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-3xl max-h-[80vh] translate-x-[-50%] translate-y-[-50%] gap-4 border border-neutral-200 bg-white p-6 shadow-lg overflow-y-auto rounded-lg">
          <div className="flex justify-between items-center">
            <Dialog.Title className="text-xl font-semibold">
              {title || i18n.translate("pressReleaseDetails")}
            </Dialog.Title>
            <Dialog.Close asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-8 w-8 rounded-full absolute top-2 right-2"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </Button>
            </Dialog.Close>
          </div>

          <div className="py-4">
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
              </div>
            ) : error ? (
              <div className="text-red-500 p-4 bg-red-50 rounded">
                {i18n.translate("errorLoadingData")}: {error.message}
              </div>
            ) : data ? (
              <div className="prose max-w-none">
                <Markdown>{data.message || ""}</Markdown>
              </div>
            ) : (
              <div className="text-gray-500 text-center py-4">
                {i18n.translate("noDataAvailable")}
              </div>
            )}
          </div>

          <div className="flex justify-end mt-4">
            <Dialog.Close asChild>
              <Button variant="outline">
                {i18n.translate("close")}
              </Button>
            </Dialog.Close>
          </div>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
