import { RecentIcontype } from "@/modules/Dashboard/RecentIcontype";
import type { RecentType, RecentUpdatesProps } from "@/types/dashboard";
import { ExternalLink } from "lucide-react";
import { i18n } from "@euroland/libs";
import { useState } from "react";
import { PressReleaseModal } from "@/components/Dashboard/PressReleaseModal";

const VISIBLE_ITEMS = 20;

const RecentUpdatesList = ({ updates }: RecentUpdatesProps) => {
  const [selectedPressReleaseId, setSelectedPressReleaseId] = useState<string | null>(null);
  const [selectedPressReleaseTitle, setSelectedPressReleaseTitle] = useState<string>("");
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleViewDetails = (id: string, title: string) => {
    setSelectedPressReleaseId(id);
    setSelectedPressReleaseTitle(title);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPressReleaseId(null);
  };

  return (
    <div className="lg:col-span-2">
      <div className="bg-white rounded shadow p-4 mb-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">
            {i18n.translate("recentUpdates")}
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Latest news, events, and price movements from your followed companies
          </p>
        </div>
        <div className="space-y-4 pr-4">
          {updates.slice(0, VISIBLE_ITEMS).map((update, index) => (
            <div
              key={index}
              className="flex items-start space-x-4 pb-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 p-2 rounded transition-colors"
            >
              <RecentIcontype recentType={update?.messageType?.name as RecentType} />
              <div className="flex-grow">
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-700">
                    {update?.company?.companyName}
                  </span>
                  <span className="text-xs text-gray-500">{update?.date}</span>
                </div>
                <p className="mt-1 text-sm font-medium">{update?.title}</p>
                <div className="flex items-center mt-1">
                  <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded">
                    {update?.messageType?.name}
                  </span>
                  <button
                    onClick={() => handleViewDetails(update?.id, update?.title)}
                    className="text-xs text-blue-600 hover:text-blue-800 ml-2 flex items-center"
                  >
                    {i18n.translate("viewDetails")}
                  </button>
                  <ExternalLink className="text-blue-600 lucide lucide-external-link w-3 h-3 ml-1" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <PressReleaseModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        pressReleaseId={selectedPressReleaseId}
        title={selectedPressReleaseTitle}
      />
    </div>
  );
};

export default RecentUpdatesList;