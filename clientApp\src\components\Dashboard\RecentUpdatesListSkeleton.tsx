import { Skeleton } from "@/components/ui/skeleton";
import { i18n } from "@euroland/libs";
const VISIBLE_ITEMS = 6;

const RecentUpdatesListSkeleton = () => {
  
  return (
    <div className="lg:col-span-2">
      <div className="bg-white rounded shadow p-4 mb-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold">
            {i18n.translate("recentUpdates")}
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            Latest news, events, and price movements from your followed companies
          </p>
        </div>
        <div className="space-y-4 pr-4">
          {Array.from({ length: VISIBLE_ITEMS }).map((_, index) => (
            <div
              key={index}
              className="flex items-start space-x-4 pb-4 border-b border-gray-100 last:border-b-0 p-2 rounded"
            >
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="flex-grow">
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-24 mb-2" />
                  <Skeleton className="h-3 w-16" />
                </div>
                <Skeleton className="h-4 w-full mb-2" />
                <div className="flex items-center mt-1">
                  <Skeleton className="h-6 w-16 rounded" />
                  <Skeleton className="h-4 w-20 ml-2" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RecentUpdatesListSkeleton; 