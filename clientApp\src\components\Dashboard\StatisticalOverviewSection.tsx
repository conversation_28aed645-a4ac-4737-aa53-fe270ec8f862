import { useEffect, useState } from "react";
import type { OverviewItem } from "@/types/dashboard";
import StatisticsCards from "./StatisticsCards";
import StatisticsCardsSkeleton from "./StatisticsCardsSkeleton";
import { useStatisticalOverview } from "@/hooks/useQueries";
import { createOverviewData } from "@/lib/utils";

const StatisticalOverviewSection = () => {
  const [dataOverview, setDataOverview] = useState<OverviewItem[]>([]);
  const { data: statisticsData, isLoading: isStatisticsLoading } = useStatisticalOverview();

  useEffect(() => {
    if (statisticsData && !isStatisticsLoading) {
      const overviewData = createOverviewData(statisticsData);
      setDataOverview(overviewData);
    }
  }, [statisticsData, isStatisticsLoading]);

  return (
    <>
      {isStatisticsLoading ? (
        <StatisticsCardsSkeleton />
      ) : (
        <StatisticsCards dataOverview={dataOverview} />
      )}
    </>
  );
};

export default StatisticalOverviewSection; 