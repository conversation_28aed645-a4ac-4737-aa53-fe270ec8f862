import CountUp from "react-countup";
import type { StatisticsProps } from "@/types/dashboard";

const StatisticsCards = ({ dataOverview }: StatisticsProps) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
    {dataOverview?.map((item) => (
      <div className="bg-white p-4 rounded shadow" key={item.title}>
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium text-gray-500">{item.title}</h3>
          {item.icon}
        </div>
        <p className="text-2xl font-bold">
          <CountUp end={item.count} />
        </p>
        <p className="text-xs text-gray-500 mt-1">{item.description}</p>
      </div>
    ))}
  </div>
);

export default StatisticsCards; 