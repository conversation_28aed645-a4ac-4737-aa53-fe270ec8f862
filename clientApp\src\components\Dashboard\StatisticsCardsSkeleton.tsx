import { Skeleton } from "@/components/ui/skeleton";

const StatisticsCardsSkeleton = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
    {[1, 2, 3].map((item) => (
      <div className="bg-white p-4 rounded shadow" key={item}>
        <div className="flex justify-between items-center mb-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-5 w-5 rounded-full" />
        </div>
        <Skeleton className="h-8 w-16 my-2" />
        <Skeleton className="h-3 w-28 mt-1" />
      </div>
    ))}
  </div>
);

export default StatisticsCardsSkeleton; 