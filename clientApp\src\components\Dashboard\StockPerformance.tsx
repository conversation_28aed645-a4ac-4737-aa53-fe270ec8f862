import type { StockPerformanceProps } from "@/types/dashboard";
import { Link } from "react-router-dom";
import { ArrowUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { i18n } from "@euroland/libs";
import { useMemo } from "react";

const StockPerformance = ({ companies }: StockPerformanceProps) => {
  const reversedCompanies = useMemo(() => {
    return [...(companies || [])].reverse();
  }, [companies]);

  return (
  <div className="space-y-6">
    <div className="bg-white p-4 rounded-lg shadow">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">
          {i18n.translate("stockPerformance")}
        </h2>
        <div className="flex items-center">
          <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-1"></span>
          <span className="text-xs text-gray-500">
            {i18n.translate("marketsOpen")}
          </span>
        </div>
      </div>
      <div>
        {reversedCompanies.map((item, index) => (
          <div
            key={index}
            className="border-b pb-3 mb-3 last:border-b-0 last:pb-0"
          >
            <p className="text-sm font-medium mb-2">{item?.companyName}</p>
            <div className="space-y-1 pl-2">
              {item?.instruments?.map((instrument, indexChildren) => (
                instrument?.currentPrice && (
                  <div
                    key={indexChildren}
                    className="flex items-center justify-between p-1.5 rounded bg-gray-50 border-b border-gray-100"
                  >
                    <div className="flex items-center">
                      <span className="text-xs font-medium bg-gray-200 text-gray-700 px-1.5 py-0.5 rounded mr-2 min-w-10 text-center">
                        {instrument?.market?.abbreviation ?? "---"}
                      </span>
                      <p className="text-xs text-gray-500">
                        {instrument?.currency?.code} {instrument?.currentPrice?.last}
                      </p>
                    </div>
                    <div
                      className={cn(
                        "flex items-center",
                        instrument?.currentPrice?.changePercentage >= 0
                          ? "text-green-600"
                          : "text-red-600"
                      )}
                    >
                      <ArrowUp
                        className={cn(
                          "w-4 h-4 mr-1",
                          instrument?.currentPrice?.changePercentage < 0 && "rotate-180"
                        )}
                      />
                      <span className="text-sm font-medium">
                        {instrument?.currentPrice?.changePercentage}
                      </span>
                    </div>
                  </div>
                )
              ))}
            </div>
          </div>
        ))}
      </div>
      <div className="mt-4 text-center">
        <Link
          to={"/followed-companies"}
          className="w-full text-center text-sm text-blue-600 hover:text-blue-800"
        >
          {i18n.translate("viewAllFollowedCompanies")}
        </Link>
      </div>
    </div>
  </div>
)};

export default StockPerformance; 