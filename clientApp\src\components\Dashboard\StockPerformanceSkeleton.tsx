import { Skeleton } from "@/components/ui/skeleton";
import { i18n } from "@euroland/libs";

const StockPerformanceSkeleton = () => (
  <div className="space-y-6">
    <div className="bg-white p-4 rounded-lg shadow">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">
          {i18n.translate("stockPerformance")}
        </h2>
        <div className="flex items-center">
          <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-1"></span>
          <span className="text-xs text-gray-500">
            {i18n.translate("marketsOpen")}
          </span>
        </div>
      </div>
      <div>
        {[1, 2, 3].map((item) => (
          <div
            key={item}
            className="border-b pb-3 mb-3 last:border-b-0 last:pb-0"
          >
            <Skeleton className="h-5 w-36 mb-2" />
            <div className="space-y-1 pl-2">
              {[1, 2].map((instrument) => (
                <div
                  key={instrument}
                  className="flex items-center justify-between p-1.5 rounded bg-gray-50 border-b border-gray-100"
                >
                  <div className="flex items-center">
                    <Skeleton className="h-5 w-10 rounded mr-2" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                  <div className="flex items-center">
                    <Skeleton className="h-4 w-4 rounded-full mr-1" />
                    <Skeleton className="h-4 w-10" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
      <div className="mt-4 text-center">
        <Skeleton className="h-5 w-48 mx-auto" />
      </div>
    </div>
  </div>
);

export default StockPerformanceSkeleton; 