import { useAppStore } from "@/stores/useAppStore";
import { getAvatarInitials, getImageLink } from "@/utils";
import { User } from "lucide-react";
import { useAuth } from "react-oidc-context";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

const Header = () => {
  const auth = useAuth();
  const userProfile = useAppStore((state) => state.userProfile);

  const handleLogout = () => {
    auth.signoutRedirect();
  };
  return (
    <header className="bg-euroland-navy  p-4 w-full h-[72px] flex items-center">
      <div className="w-full flex justify-between items-center">
        {" "}
        <div className="flex-shrink-0">
          <img
            src="https://tools.euroland.com/download/ac/euroland_logo.png"
            alt="Euroland Logo"
            className="h-6 pl-4"
          />{" "}
        </div>
        <div className="flex items-center gap-4 pr-4">
          {" "}
          {userProfile.id && (
            <>
              <div>
                {userProfile.avatar ? (
                  <Avatar className="w-10 h-10 cursor-pointer">
                    <AvatarImage
                      src={getImageLink(userProfile.avatar)}
                      alt={userProfile.fullName}
                    />
                    <AvatarFallback>
                      {getAvatarInitials(userProfile.fullName)}
                    </AvatarFallback>
                  </Avatar>
                ) : (
                  <div className="w-10 h-10 bg-euroland-blue flex items-center justify-center flex-shrink-0 rounded-full">
                    <User size={24} className="text-white" />
                  </div>
                )}
              </div>
              <span className="text-sm font-medium text-white">
                {userProfile.fullName}
              </span>
            </>
          )}
          <div className="w-px h-6 bg-white/30"></div>
          <button
            className="text-sm bg-euroland-blue hover:bg-opacity-80 px-3 py-1 rounded text-white"
            onClick={handleLogout}
          >
            Logout
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
