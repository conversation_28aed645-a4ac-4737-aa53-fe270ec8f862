import { TABS } from "@/constants/appConstants";
import { <PERSON> } from "react-router-dom";
import { useLocation } from "react-router-dom";

const NavMenu = () => {
  const location = useLocation();
  return (
    <nav className="w-64 bg-white border-r border-gray-200 p-4 pb-2 overflow-y-auto">
      {TABS.map((tab) => (
        <Link
          key={tab.title}
          className={`w-full text-left px-4 py-2 mb-2 text-sm font-medium border-b border-gray-200 last:border-b-0 inline-block cursor-pointer	 ${
            location.pathname === tab.href
              ? "bg-gray-100 text-gray-900 font-bold"
              : "text-gray-600 hover:bg-gray-100"
          }`}
          to={tab.href}
        >
          {tab.title}
        </Link>
      ))}
    </nav>
  );
};

export default NavMenu;
