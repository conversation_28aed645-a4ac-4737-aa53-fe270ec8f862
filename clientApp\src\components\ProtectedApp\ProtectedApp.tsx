import { Loader2 } from "lucide-react";
import { type ReactNode, useEffect, useState } from "react";
import { hasAuthParams, useAuth } from "react-oidc-context";

interface ProtectedAppProps {
  children: ReactNode;
}

const ProtectedApp: React.FC<ProtectedAppProps> = (props) => {
  const { children } = props;

  const auth = useAuth();
  const [hasTriedSignin, setHasTriedSignin] = useState(false);

  /**
   * Do auto sign in.
   *
   * See {@link https://github.com/authts/react-oidc-context?tab=readme-ov-file#automatic-sign-in}
   */
  useEffect(() => {
    if (
      !(
        hasAuthParams() ||
        auth.isAuthenticated ||
        auth.activeNavigator ||
        auth.isLoading ||
        hasTriedSignin
      )
    ) {
      void auth.signinRedirect();
      setHasTriedSignin(true);
    }
  }, [auth, hasTriedSignin]);

  useEffect(() => {
    return auth.events.addSilentRenewError(() => {
      void auth.removeUser();
      void auth.signinRedirect();
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [auth.events, auth.signinRedirect]);

  return (
    <>
      {auth.error ? (
        <>
          <h1>We've hit a snag</h1>
          <p className="text-red-500">{auth.error?.message}</p>
        </>
      ) : auth.isLoading ? (
        <div className="w-full h-[100vh] flex items-center justify-center">
          <Loader2 className="animate-spin" />
        </div>
      ) : auth.isAuthenticated ? (
        children
      ) : null}
    </>
  );
};

export default ProtectedApp;
