import { useAuth } from "react-oidc-context";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const auth = useAuth();

  if (auth.isLoading) {
    return <div><PERSON><PERSON> kiểm tra phiên đăng nhập...</div>;
  }

  if (!auth.isAuthenticated) {
    auth.signinRedirect();
    return null; // hoặc <Navigate to="/login" /> nếu có route login riêng
  }

  return <>{children}</>;
}
