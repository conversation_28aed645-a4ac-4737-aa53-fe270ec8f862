import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAppStore } from "@/stores/useAppStore";
import { addTimestamp, getAvatarInitials, getImageLink } from "@/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Camera } from "lucide-react";
import { useRef } from "react";
import { i18n } from "@euroland/libs";
import { useUserAvatar } from "@/hooks/useQueries";

interface UploadAvatarProps {
  onAvatarUrlChange?: (url: string) => void;
  avatarUploadUrl: string;
}

const UploadAvatar: React.FC<UploadAvatarProps> = ({
  onAvatarUrlChange,
  avatarUploadUrl,
}) => {
  const userProfile = useAppStore((state) => state.userProfile);
  const { fullName } = userProfile;
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { uploadAvatar, isLoading: isUploading } = useUserAvatar();
  const handleAvatarClick = () => {
    if (isUploading) return;
    fileInputRef.current?.click();
  };
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;
    const avatarUrl = await uploadAvatar(file);
    if (avatarUrl) {
      onAvatarUrlChange?.(addTimestamp(avatarUrl as string));
    }
  };
  return (
    <div className="mb-6 flex items-center">
      <Avatar className="w-24 h-24 cursor-pointer">
        <AvatarImage src={getImageLink(avatarUploadUrl)} alt={fullName} />
        <AvatarFallback>{getAvatarInitials(fullName)}</AvatarFallback>
      </Avatar>
      <div className="ml-4">
        <Button variant="outline" size="sm" onClick={handleAvatarClick}>
          <Camera className="mr-2 h-4 w-4" />
          {isUploading
            ? `${i18n.translate("uploading")}...`
            : i18n.translate("changeAvatar")}
        </Button>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept="image/*"
          className="hidden"
        />
      </div>
    </div>
  );
};

export default UploadAvatar;
