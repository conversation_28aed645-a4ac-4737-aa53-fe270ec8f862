import Header from "../Header";
import Footer from "../Footer";
import NavMenu from "../NavMenu";
import { useAppStore } from "@/stores/useAppStore";
import { Toaster } from "@/components/ui/toaster";
import { useUserProfile } from "@/hooks/useQueries";
import { useEffect } from "react";
import { Outlet } from "react-router-dom";

export function AppLayout() {
  const { data } = useUserProfile();
  const setUserProfile = useAppStore((state) => state.setUserProfile);
  useEffect(() => {
    if (data) {
      setUserProfile(data);
    }
  }, [data]);
  return (
    <div className="flex flex-col h-screen">
      <Header />
      <main className="flex flex-1 overflow-hidden">
        <NavMenu />
        <div className="flex-1 overflow-y-auto bg-gray-50 p-6"><Outlet /></div>
        <Toaster />
      </main>
      <Footer />
    </div>
  );
}
