import { useState } from "react"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { UserComment } from "@/types/userComment"
import { getAvatarInitials } from "@/utils"

interface CommentsProps {
  comments: UserComment[]
}

export function Comments({ comments }: CommentsProps) {
  const [selectedCompany, setSelectedCompany] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  const filteredComments = comments.filter(
    (comment) => selectedCompany === "all" || comment.company === selectedCompany,
  )

  const indexOfLastItem = currentPage * itemsPerPage
  const indexOfFirstItem = indexOfLastItem - itemsPerPage
  const currentItems = filteredComments.slice(indexOfFirstItem, indexOfLastItem)

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber)

  return (
    <div>
      <div className="h-full overflow-y-auto pr-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Comments</h2>
          <Select value={selectedCompany} onValueChange={setSelectedCompany}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Filter by company" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Companies</SelectItem>
              <SelectItem value="nordea">Nordea Bank</SelectItem>
              <SelectItem value="fab">First Abu Dhabi Bank</SelectItem>
              <SelectItem value="adib">ADIB</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {currentItems.map((comment) => (
          <div key={comment.id} className="mb-6 border p-4">
            <div className="bg-gray-100 p-3 mb-2">
              <p className="text-sm italic">"{comment.highlightedText}"</p>
            </div>
            <p className="mb-2">{comment.comment}</p>
            <p className="text-sm text-gray-500 mb-2">{new Date(comment.timestamp).toLocaleString()}</p>
            {comment.replies.length > 0 && (
              <div className="mt-4 border-t pt-4">
                <h4 className="text-sm font-semibold mb-2">Replies:</h4>
                {comment.replies.map((reply) => (
                  <div key={reply.id} className="mb-2 bg-gray-50 p-3">
                    <div className="flex items-center gap-2 mb-1">
                      <Avatar className="w-6 h-6">
                        <AvatarImage src={`https://api.dicebear.com/6.x/initials/svg?seed=${reply.author}`} />
                        <AvatarFallback>{getAvatarInitials(reply.author)}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm font-medium">{reply.author}</span>
                    </div>
                    <p className="text-sm">{reply.content}</p>
                    <p className="text-xs text-gray-500 mt-1">{new Date(reply.timestamp).toLocaleString()}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
        <div className="mt-4 flex justify-center">
          {Array.from({ length: Math.ceil(filteredComments.length / itemsPerPage) }, (_, i) => (
            <Button
              key={i}
              onClick={() => paginate(i + 1)}
              variant={currentPage === i + 1 ? "default" : "outline"}
              size="sm"
              className="mx-1"
            >
              {i + 1}
            </Button>
          ))}
        </div>
      </div>
    </div>
  )
}

