import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { getAvatarInitials, getImageLink } from "@/utils";
import { useState } from "react";
import { i18n } from "@euroland/libs";
import { X } from "lucide-react";
import dateFormatter from "@/configs/date";
import { ModalUnFollow } from "@/modules/Follwed/ModalUnFollow";
import { Button } from "../ui/button";
import { useFollowCompanies } from "@/hooks/useQueries";
import { FollowedCompany } from "@/types/followedCompany";

export function FollowedCompanies() {
  const [isModalUnFollow, setIsModalUnFollow] = useState(false);
  const { data: followedCompanies = [] } = useFollowCompanies()

  return (
    <div className="h-full overflow-y-auto pr-4">
      <div>
        <h2 className="text-xl font-semibold mb-4 dark:text-white">
          {i18n.translate("followedCompanies")}
        </h2>
        {followedCompanies?.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {followedCompanies?.map((company: FollowedCompany) => {
              const { companyName, nextCompanyEvent } = company;
              return (
                <div
                  key={company.companyCode}
                  className="bg-white dark:bg-gray-800 border dark:border-gray-700 p-6 space-y-4 shadow-sm"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div>
                        <Avatar className="w-12 h-12 object-contain">
                          <AvatarImage
                            src={getImageLink(company.logo || "")}
                            alt={companyName}
                          />
                          <AvatarFallback>
                            {getAvatarInitials(companyName)}
                          </AvatarFallback>
                        </Avatar>
                      </div>
                      <div>
                        <h3 className="font-semibold dark:text-white">
                          {companyName}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {company.industry}
                        </p>
                      </div>
                    </div>

                    <Button
                      variant="link"
                      className="hover:text-red-50"
                      onClick={() => setIsModalUnFollow(true)}
                    >
                      <X className="w-[18px] h-[18px] " />
                    </Button>
                    <ModalUnFollow
                      isOpen={isModalUnFollow}
                      onCancel={setIsModalUnFollow}
                      companyCode={company.companyCode}
                    />
                  </div>
                  <div className="text-sm space-y-2 border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                    <p className="dark:text-gray-300">
                      <strong>Subscribed:</strong>{" "}
                      {dateFormatter.formatShortDate(company.subscribed)}
                    </p>
                    {nextCompanyEvent && (
                      <p className="dark:text-gray-300">
                        <strong>Next Financial Event:</strong>{" "}
                        {nextCompanyEvent.eventName} -{" "}
                        {dateFormatter.formatShortDate(
                          nextCompanyEvent.timeEvent
                        )}
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <p className="text-center text-gray-500 my-8">
            {i18n.translate("noFollowedCompaniesAvailable")}
          </p>
        )}
      </div>
    </div>
  );
}
