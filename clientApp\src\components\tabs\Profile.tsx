import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useAppStore } from "@/stores/useAppStore";
import UploadAvatar from "../UploadAvatar";
import Input from "../ui/input";
import { useForm, SubmitHandler } from "react-hook-form";
import { Mail, Lock, User } from "lucide-react";
import { i18n } from "@euroland/libs";
import { ModalChangePassword } from "@/modules/ProfileSettings/ModalChangePassword";
import type { IUserProfile } from "@/types/userComment";
import { toast } from "@/hooks/use-toast";
import { ModalDeleteAccount } from "@/modules/ProfileSettings/ModalDeleteAccount";
import { useUpdateUserProfile } from "@/hooks/useQueries";

export function Profile() {
  const userProfile = useAppStore((state) => state.userProfile);
  const [isDeleteAccount, setIsDeleteAccount] = useState(false);
  const [isChangePassWorld, setIsChangePassWorld] = useState(false);
  const { updateProfile } = useUpdateUserProfile();
  const { register, handleSubmit, reset, watch, formState, setValue } =
    useForm<IUserProfile>();

  const { isDirty } = formState;

  const onSubmit: SubmitHandler<IUserProfile> = async (data) => {
    try {
      await updateProfile(data);
      toast({
        variant: "success",
        title: i18n.translate("successfullyUpdatedTheInformation"),
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: i18n.translate("failedToUpdateTheInformation"),
      });
    } finally {
      reset(data, { keepValues: true });
    }
  };

  useEffect(() => {
    if (userProfile.fullName) {
      reset(userProfile);
    }
  }, [userProfile, reset]);
  return (
    <div>
      <div className="h-full overflow-y-auto pr-4">
        <h2 className="text-xl font-semibold mb-6">
          {i18n.translate("profileSettings")}
        </h2>
        <UploadAvatar
          avatarUploadUrl={watch("avatar")}
          onAvatarUrlChange={(value) => {
            setValue("avatar", value, { shouldDirty: true });
          }}
        />
        <form className="space-y-8 max-w-xl" onSubmit={handleSubmit(onSubmit)}>
          <Input
            type="text"
            {...register("fullName")}
            label={i18n.translate("name")}
            className=""
            icon={<User className="w-5 h-5" />}
          />
          <Input
            type="email"
            {...register("email")}
            label={i18n.translate("email")}
            disabled
            icon={<Mail className="w-5 h-5" />}
          />
          <div className="flex items-center gap-3">
            <Input
              type="password"
              label={i18n.translate("password")}
              value={"12345678"}
              disabled={true}
              icon={<Lock className="w-5 h-5" />}
            />
            <Button
              variant="outline"
              type="button"
              className="rounded-[6px] mt-2"
              onClick={(e) => {
                e.stopPropagation();
                setIsChangePassWorld(!isChangePassWorld);
              }}
            >
              {i18n.translate("changePassword")}
            </Button>
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {i18n.translate("notificationPreferences")}
            </h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  id="email-notifications"
                  type="checkbox"
                  {...register("notificationPreferences.isReceiveEmail")}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label
                  htmlFor="email-notifications"
                  className="ml-2 block text-sm text-gray-900"
                >
                  {i18n.translate("receiveEmailNotifications")}
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  id="browser-notifications"
                  {...register(
                    "notificationPreferences.isReceiveBrowserNotifications"
                  )}
                  type="checkbox"
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label
                  htmlFor="browser-notifications"
                  className="ml-2 block text-sm text-gray-900"
                >
                  {i18n.translate("receiveBrowserNotifications")}
                </label>
              </div>
            </div>
          </div>
          <div>
            <Button
              onClick={(e) => {
                e.stopPropagation();
              }}
              disabled={!isDirty}
              type="submit"
              className="w-full md:w-auto rounded-[6px]"
            >
              {i18n.translate("saveChanges")}
            </Button>
          </div>
          <Button
            type="button"
            onClick={() => setIsDeleteAccount(true)}
            className="w-full md:w-auto bg-red-50 rounded-[6px]"
          >
            {i18n.translate("deleteAccount")}
          </Button>
        </form>
        <ModalChangePassword
          isOpen={isChangePassWorld}
          onCancel={setIsChangePassWorld}
        />
        <ModalDeleteAccount
          isOpen={isDeleteAccount}
          onCancel={setIsDeleteAccount}
        />
      </div>
    </div>
  );
}
