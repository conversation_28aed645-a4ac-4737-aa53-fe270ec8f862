import { useMemo, useState } from "react";
import { Loader2, Trash2 } from "lucide-react";
import { uniqBy } from "es-toolkit";
import { Button } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import dateFormatter from "@/configs/date";
import { Markdown } from "../Markdown";
import { i18n } from "@euroland/libs";
import { ModalClearHistory } from "@/modules/SearchHistory/ModalClearHistory";
import { useDeleteSearchHistory, useSearchHistories } from "@/hooks/useQueries";

export function SearchHistory() {
  const { data: searchHistories = [], isLoading } = useSearchHistories()
  
  const [selectedCompany, setSelectedCompany] = useState("all");
  const [showClearConfirmation, setShowClearConfirmation] = useState(false);
  const filteredHistory = useMemo(
    () =>
      selectedCompany === "all"
        ? [...searchHistories]
        : searchHistories.filter(
          (item) => item.companyCode === selectedCompany
        ),
    [searchHistories, selectedCompany]
  );

  const handleShowClearConfirmation = () => {
    if (isLoading) return;
    setShowClearConfirmation((prev) => !prev);
  };

  const handleDeleteHistory = async (historyId: string) => {
    try {
      const { deleteSearchHistory } = useDeleteSearchHistory(historyId);
      await deleteSearchHistory();
      filteredHistory.filter((item) => item.id !== historyId);
    } catch (error) {
      console.error(error);
    }
  };

  const companies = useMemo(() => {
    const defaultAll = [
      {
        companyName: "all",
        companyCode: "all",
      },
      {
        companyName: "Abu Dhabi Islamic Bank",
        companyCode: "abuDhabiIslamicBank",
      },
      {
        companyCode: "firstAbuDhabiBank",
        companyName: "First Abu Dhabi Bank",
      },
    ];
    return [
      ...defaultAll,
      ...uniqBy(
        searchHistories.map((item) => ({
          companyName: item.companyName,
          companyCode: item.companyCode,
        })),
        (item) => item.companyCode
      ).sort(),
    ];
  }, [searchHistories]);

  return (
    <div className="h-full overflow-y-auto pr-4 ">
      <div className="flex justify-between items-center mb-4 mt-1">
        <h2 className="text-xl font-semibold">
          {i18n.translate("searchHistory")}
        </h2>
        <div className="flex items-center space-x-4">
          <Select value={selectedCompany} onValueChange={setSelectedCompany}>
            <SelectTrigger className="w-[200px] rounded-[6px]">
              <SelectValue placeholder="Filter by company" />
            </SelectTrigger>
            <SelectContent>
              {companies.map((company) => (
                <SelectItem
                  key={company.companyCode}
                  value={company.companyCode}
                >
                  {company.companyCode === "all"
                    ? "All Companies"
                    : company.companyName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {filteredHistory.length > 0 && (
            <div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowClearConfirmation(true)}
                className="text-red-500 border-red-500 hover:bg-red-10 rounded-[6px]"
              >
                {isLoading && <Loader2 className="animate-spin" />}
                {i18n.translate("clearHistory")}
              </Button>
              <ModalClearHistory
                isOpen={showClearConfirmation}
                onCancel={handleShowClearConfirmation}
              />
            </div>
          )}
        </div>
      </div>
      {filteredHistory.length > 0 ? (
        <>
          <Accordion type="single" collapsible className="w-full space-y-4">
            {filteredHistory.map((item) => (
              <AccordionItem value={item.id} key={item.id} className="border">
                <AccordionTrigger className="px-4 py-2 hover:no-underline [&[data-state=open]>div]:pb-2 [&[data-state=open]]:pb-0 [&>svg]:hidden transition-all duration-300 ease-in-out">
                  <div className="flex flex-col items-start w-full text-left">
                    <h3 className="font-medium">{item.question}</h3>
                    <div className="flex justify-between w-full mt-1 text-sm text-gray-500">
                      <span>
                        {i18n.translate("company")}: {item.companyName}
                      </span>
                      <span>
                        {dateFormatter.formatShortDate(item.searchTime)}
                      </span>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 pb-4">
                  <div className="prose prose-sm max-w-none relative">
                    <button
                      onClick={() => handleDeleteHistory(item.id)}
                      className="absolute top-2 right-2 p-1 text-gray-400 hover:text-red-500"
                      aria-label="Delete search history item"
                    >
                      <Trash2 size={16} />
                    </button>
                    <h4 className="text-lg font-semibold mb-2">
                      {i18n.translate("answer")}:
                    </h4>
                    <div className="space-y-4">
                      <Markdown>{item.aiResult}</Markdown>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </>
      ) : (
        <p className="text-center text-gray-500 my-8">
          {i18n.translate("noSearchHistoryAvailable")}
        </p>
      )}
    </div>
  );
}
