import { ExternalLink, Building2, Landmark, CircleDollarSign, Building } from "lucide-react"
import type { Subscription } from "@/types/subscription"

interface SubscriptionsProps {
  subscriptions: Subscription[]
}

export function Subscriptions({ subscriptions }: SubscriptionsProps) {
  return (
    <div>
      <div className="h-full overflow-y-auto pr-4">
        <h2 className="text-xl font-semibold mb-4">Subscriptions</h2>
        <p className="text-gray-600 mb-6">
          These are the companies from which you receive email notifications. You can manage your subscriptions and
          access investor relations information for each company.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
          {subscriptions.map((company, index) => (
            <div key={index} className="bg-white border rounded-lg p-6 flex flex-col shadow-sm">
              <div className="flex items-center mb-4">
                <div>
                  {(() => {
                    switch (company.name) {
                      case "Nordea Bank":
                        return <Building2 className="w-12 h-12 mr-4 text-blue-600" />
                      case "First Abu Dhabi Bank":
                        return <Landmark className="w-12 h-12 mr-4 text-green-600" />
                      case "Abu Dhabi Islamic Bank":
                        return <CircleDollarSign className="w-12 h-12 mr-4 text-purple-600" />
                      default:
                        return <Building className="w-12 h-12 mr-4 text-gray-600" />
                    }
                  })()}
                </div>
                <h3 className="text-lg font-semibold">{company.name}</h3>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Subscribed since: {new Date(company.subscribedDate).toLocaleDateString()}
              </p>
              <div className="mb-4">
                <h4 className="text-sm font-semibold mb-1">Upcoming Event:</h4>
                <p className="text-sm">{company.upcomingEvent.name}</p>
                <p className="text-xs text-gray-600">{new Date(company.upcomingEvent.date).toLocaleDateString()}</p>
              </div>
              <div className="mb-4">
                <h4 className="text-sm font-semibold mb-1">Latest Press Release:</h4>
                <p className="text-sm">{company.latestPressRelease.title}</p>
                <p className="text-xs text-gray-600">
                  {new Date(company.latestPressRelease.date).toLocaleDateString()}
                </p>
              </div>
              <a
                href={company.irPageLink}
                target="_blank"
                rel="noopener noreferrer"
                className="mt-auto text-blue-600 hover:text-blue-800 flex items-center"
              >
                Visit Investor Relations Page
                <ExternalLink className="h-4 w-4 ml-1" />
              </a>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

