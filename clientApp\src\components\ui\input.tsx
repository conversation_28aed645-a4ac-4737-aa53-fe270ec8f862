"use client";
import { cn } from "@/lib/utils";
import { forwardRef, useState } from "react";
import { UseFormRegisterReturn } from "react-hook-form";
import { Eye, EyeOff } from "lucide-react";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  classLabel?: string;
  icon?: React.ReactNode;
  register?: UseFormRegisterReturn;
  showIconPassWorld?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      classLabel,
      showIconPassWorld = false,
      icon,
      className,
      register,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    return (
      <div className="mb-4">
        {label && (
          <label
            htmlFor={props.name}
            className={`block text-sm font-medium text-gray-700 ${classLabel}`}
          >
            {label}
          </label>
        )}
        <div className="relative">
          {icon && (
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
              {icon}
            </div>
          )}
          <input
            {...props}
            {...register}
            type={
              showIconPassWorld
                ? showPassword
                  ? "text"
                  : "password"
                : props.type
            }
            ref={ref}
            className={cn(
              `mt-1 block w-full h-[42px] shadow-sm bg-white rounded-[6px] border border-solid border-gray-300 ${
                props.disabled ? "cursor-not-allowed" : ""
              }  ${icon ? "pl-11" : ""}`,
              className
            )}
          />
          {props.type === "password" && showIconPassWorld && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showPassword ? (
                <Eye className="w-4 h-4" />
              ) : (
                <EyeOff className="w-4 h-4" />
              )}
            </button>
          )}
        </div>
      </div>
    );
  }
);

export default Input;
