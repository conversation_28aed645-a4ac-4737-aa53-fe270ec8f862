import { FORMAT_DATE } from '@/constants/appConstants';
import dayjs, { Dayjs } from 'dayjs';

class DateFormatter {
  private static instance: DateFormatter;

  private constructor() {}

  public static getInstance(): DateFormatter {
    if (!DateFormatter.instance) {
      DateFormatter.instance = new DateFormatter();
    }
    return DateFormatter.instance;
  }
  
  public formatShortDate(date: Date | Dayjs | string): string {
    return dayjs(date).format(FORMAT_DATE.SHORT_DATE);
  }
}

const dateFormatter = DateFormatter.getInstance();

export default dateFormatter;
