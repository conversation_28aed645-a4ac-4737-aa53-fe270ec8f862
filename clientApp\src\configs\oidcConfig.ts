import { UserManager, WebStorageStateStore } from "oidc-client-ts";

/**
 * An object containing the routes used for OIDC (OpenID Connect) authentication.
 *
 * @property {string} SIGNIN - The route for the sign-in page.
 * @property {string} SIGNIN_CALLBACK - The route for the sign-in callback page.
 * @property {string} SIGNOUT - The route for the sign-out page.
 * @property {string} SIGNOUT_CALLBACK - The route for the sign-out callback page.
 */
export const OidcAuthAppRoutes = {
  SIGNIN: '/signin',
  SIGNIN_CALLBACK: '/signin-oidc',
  SIGNOUT: '/signout',
  SIGNOUT_CALLBACK: '/signout-oidc'
};

function getBaseUrl(): string {
  let baseUrl = (document.querySelector('base') || {} as HTMLBaseElement).href;

  if(/\/$/.test(baseUrl)) {
    baseUrl = baseUrl.replace(/\/$/, '');
  }

  return baseUrl;
}

export const userManager  = new UserManager({
  authority: import.meta.env.VITE_IDENTITY_URL,
  client_id: import.meta.env.VITE_IDENTITY_CLIENT_ID,                   
  redirect_uri: `${getBaseUrl()}${OidcAuthAppRoutes.SIGNIN_CALLBACK}`,
  post_logout_redirect_uri: `${getBaseUrl()}${OidcAuthAppRoutes.SIGNOUT_CALLBACK}`,
  response_type: "code",                         
  scope: 'openid profile',
  userStore: new WebStorageStateStore({ store: window.localStorage }),      
  automaticSilentRenew: true,  
  silent_redirect_uri: `${getBaseUrl()}${OidcAuthAppRoutes.SIGNIN_CALLBACK}`,
  // monitorSession: true,
});
