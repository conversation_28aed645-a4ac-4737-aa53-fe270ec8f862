import type { Subscription } from "@/types/subscription"

export const subscriptions: Subscription[] = [
  {
    name: "Nordea Bank",
    subscribedDate: "2023-05-15",
    upcomingEvent: {
      name: "Q2 2024 Earnings Call",
      date: "2024-07-18",
    },
    latestPressRelease: {
      title: "Nordea to Acquire Danske Bank's Norwegian Personal Banking Business",
      date: "2024-03-06",
    },
    irPageLink: "https://www.nordea.com/en/investors",
  },
  {
    name: "First Abu Dhabi Bank",
    subscribedDate: "2023-08-22",
    upcomingEvent: {
      name: "Annual General Meeting",
      date: "2024-04-10",
    },
    latestPressRelease: {
      title: "FAB Reports Strong Q1 2024 Results",
      date: "2024-04-02",
    },
    irPageLink: "https://www.bankfab.com/en-ae/about-fab/investor-relations",
  },
  // Add more subscriptions as needed
]

