import type { UserComment } from "@/types/userComment"

export const userComments: UserComment[] = [
  {
    id: "comment-1",
    comment: "This is a significant move for Nordea. How will this affect their market position in Norway?",
    highlightedText: "Nordea to acquire Danske Bank's personal customer business in Norway",
    timestamp: "2024-03-15T10:30:00Z",
    replies: [
      {
        id: "reply-1",
        author: "Nordea IR Team",
        content:
          "This acquisition is expected to strengthen our position in the Norwegian market. We'll provide more details in our upcoming investor call.",
        timestamp: "2024-03-15T11:45:00Z",
      },
    ],
    company: "nordea",
  },
  {
    id: "comment-2",
    comment: "Impressive results. What's driving the increase in net interest income?",
    highlightedText: "Net interest income rose by 20%",
    timestamp: "2024-03-10T14:20:00Z",
    replies: [],
    company: "nordea",
  },
  // Add more comments as needed
]

