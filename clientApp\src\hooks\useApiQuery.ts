import axiosInstance, { aiSearchAxiosInstance } from "@/services/apiClient";
import { DefinedInitialDataOptions, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";

export const useApiQuery = <T>(
  path: string, 
  options?: AxiosRequestConfig, 
  useQueryOptions?: Omit<DefinedInitialDataOptions<T>, 'queryKey' | 'queryFn' | 'initialData'>,
  axiosClient: AxiosInstance = axiosInstance
) => {
  return useQuery<T>({
    ...useQueryOptions,
    refetchOnReconnect: true, 
    refetchOnWindowFocus: true,
    queryKey: [path, JSON.stringify(options)].filter(Boolean),
    queryFn: async () => (await axiosClient.get(path, options)).data,
  });
};

export const useApiMutation = <TResponse, TData = unknown>(
  path: string, 
  invalidatePaths?: string[], 
  method: 'post' | 'put' | 'delete' = 'post',
  axiosClient: AxiosInstance = axiosInstance
) => {
  const queryClient = useQueryClient();
  return useMutation<AxiosResponse<TResponse>, Error, TData>({
    mutationFn: (data: TData) => {
      const axiosConfig: AxiosRequestConfig = { timeout: 60000 * 5 };
      switch (method) {
        case 'post':
          if (data instanceof FormData) {
            return axiosClient.postForm(path, data, axiosConfig);
          }
          return axiosClient.post(path, data, axiosConfig);
        case 'put':
          if (data instanceof FormData) {
            return axiosClient.putForm(path, data, axiosConfig);
          }
          return axiosClient.put(path, data, axiosConfig);
        case 'delete':
          return axiosClient.delete(path, axiosConfig);
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: invalidatePaths ? invalidatePaths : [path] });
    },
  });
};

export const useAiSearchApiQuery = <T>(
  path: string, 
  options?: AxiosRequestConfig, 
  useQueryOptions?: Omit<DefinedInitialDataOptions<T>, 'queryKey' | 'queryFn' | 'initialData'>
) => {
  return useApiQuery<T>(path, options, useQueryOptions, aiSearchAxiosInstance);
};

export const useAiSearchApiMutation = <TResponse, TData = unknown>(
  path: string, 
  invalidatePaths?: string[], 
  method: 'post' | 'put' | 'delete' = 'post'
) => {
  return useApiMutation<TResponse, TData>(path, invalidatePaths, method, aiSearchAxiosInstance);
};

