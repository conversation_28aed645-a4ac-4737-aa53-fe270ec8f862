import { API_PATHS } from "@/services/apiPath";
import { useApiQuery, useApiMutation, useAiSearchApiQuery, useAiSearchApiMutation } from "./useApiQuery";
import type { IEditDataApi, IUserProfile } from "@/types/userComment";
import { removeTimestamp } from "@/utils";
import { DEFAULT_LANGUAGE } from "@/constants/appConstants";
import { ChangePasswordType } from "@/types/profile";
import { IStatisticsData } from "@/lib/utils";
import { FollowedCompany } from "@/types/followedCompany";
import { SearchHistoryItem } from "@/types/searchHistory";

interface ApiResponse<T> {
    data: T;
}

export const useStatisticalOverview = () => {
    const { data, isLoading, error } = useApiQuery<ApiResponse<IStatisticsData>>(
        API_PATHS.DASHBOARD.STATISTICAL_OVERVIEW,
        undefined,
        { staleTime: 600000 }
    );
    return { data: data?.data, isLoading, error };
};

export const useUserProfile = () => {
    const { data, isLoading, error } = useApiQuery<ApiResponse<IUserProfile>>(
        API_PATHS.USER.PROFILE,
        undefined,
        { staleTime: 600000 }
    );
    return { data: data?.data, isLoading, error };
};

export const useUpdateUserProfile = () => {
    const mutation = useApiMutation<ApiResponse<IEditDataApi>, IEditDataApi>(
        API_PATHS.USER.PROFILE,
        [API_PATHS.USER.PROFILE],
        'put'
    );

    const updateProfile = async (editData: IEditDataApi): Promise<IEditDataApi | unknown> => {
        const avatar = editData.avatar ? removeTimestamp(editData.avatar) : "";
        try {
            const response = await mutation.mutateAsync({
                ...editData,
                avatar,
            });
            return response?.data?.data;
        } catch (error: unknown) {
            if (error instanceof Error) {
                return error.message;
            }
            return String(error);
        }
    };

    return {
        updateProfile,
        isLoading: mutation.isPending,
        error: mutation.error,
        isSuccess: mutation.isSuccess
    };
};

export const useFollowCompanies = () => {
    const { data, isLoading, error, refetch } = useApiQuery<ApiResponse<FollowedCompany[]>>(
        API_PATHS.USER.FOLLOWING_COMPANIES,
        {
            params: {
                language: DEFAULT_LANGUAGE,
            },
        },
        { staleTime: 600000 }
    );
    return {
        data: data?.data || [],
        isLoading,
        error,
        refetch
    };
}

interface UnfollowResponse {
    success: boolean;
    message?: string;
}

export const useUnFollowCompany = (companyCode: string) => {
    const mutation = useApiMutation<ApiResponse<UnfollowResponse>, Record<string, never>>(
        `${API_PATHS.USER.FOLLOW_COMPANY}?companyCode=${companyCode}&status=2`,
        [API_PATHS.USER.FOLLOW_COMPANY],
        'post'
    );

    return {
        unFollowCompany: () => mutation.mutateAsync({}),
        isLoading: mutation.isPending,
        error: mutation.error,
        isSuccess: mutation.isSuccess
    };
}

interface AvatarResponse {
    avatarUrl: string;
}

export const useUserAvatar = () => {
    const mutation = useApiMutation<ApiResponse<AvatarResponse>, FormData>(
        API_PATHS.USER.UPLOAD_AVATAR,
        [API_PATHS.USER.UPLOAD_AVATAR],
        'post'
    );

    const uploadAvatar = async (file: File): Promise<string | unknown> => {
        try {
            const formData = new FormData();
            formData.append("avatar", file);
            const response = await mutation.mutateAsync(formData);
            return response?.data?.data;
        } catch (error: unknown) {
            if (error instanceof Error) {
                return error.message;
            }
            return String(error);
        }
    };
    return {
        uploadAvatar,
        isLoading: mutation.isPending,
        error: mutation.error,
        isSuccess: mutation.isSuccess
    };
}

interface PasswordChangeResponse {
    success: boolean;
    message?: string;
}

export const useChangePassword = () => {
    const mutation = useApiMutation<ApiResponse<PasswordChangeResponse>, ChangePasswordType>(
        API_PATHS.USER.CHANGE_PASSWORLD,
        [API_PATHS.USER.CHANGE_PASSWORLD],
        'post'
    );

    const changePassword = async (params: ChangePasswordType): Promise<PasswordChangeResponse | unknown> => {
        try {
            const response = await mutation.mutateAsync(params);
            return response?.data?.data;
        } catch (error: unknown) {
            if (error instanceof Error) {
                return error.message;
            }
            return String(error);
        }
    };

    return {
        changePassword,
        isLoading: mutation.isPending,
        error: mutation.error,
        isSuccess: mutation.isSuccess
    };
}

export const useSearchHistories = () => {
    const { data, isLoading, error, refetch } = useAiSearchApiQuery<{ data: SearchHistoryItem[] }>(
        API_PATHS.SEARCH.SEARCH_HISTORIES,
        {
            params: {
                language: DEFAULT_LANGUAGE,
            },
        },
        { staleTime: 600000 }
    );
    return {
        data: data?.data || [],
        isLoading,
        error,
        refetch
    };
}

export const useDeleteSearchHistory = (historyId: string) => {
    const mutation = useAiSearchApiMutation<ApiResponse<SearchHistoryItem>, string>(
        `API_PATHS.SEARCH.DELETE_SEARCH_HISTORIES/${historyId}`,
        [API_PATHS.SEARCH.SEARCH_HISTORIES],
        'delete'
    );

    const deleteSearchHistory = async (): Promise<SearchHistoryItem | unknown> => {
        try {
            const response = await mutation.mutateAsync("");
            return response?.data?.data;
        } catch (error: unknown) {
            if (error instanceof Error) {
                return error.message;
            }
            return String(error);
        }
    };

    return {
        deleteSearchHistory,
        isLoading: mutation.isPending,
        error: mutation.error,
        isSuccess: mutation.isSuccess
    };
}

export const useDeleteAllSearchHistories = () => {
    const mutation = useAiSearchApiMutation<ApiResponse<SearchHistoryItem>, string>(
        API_PATHS.SEARCH.DELETE_ALL,
        [API_PATHS.SEARCH.SEARCH_HISTORIES],
        'delete'
    );

    const deleteAllSearchHistories = async (): Promise<SearchHistoryItem | unknown> => {
        try {
            const response = await mutation.mutateAsync("");
            return response?.data?.data;
        } catch (error: unknown) {
            if (error instanceof Error) {
                return error.message;
            }
            return String(error);
        }
    };

    return {
        deleteAllSearchHistories,
        isLoading: mutation.isPending,
        error: mutation.error,
        isSuccess: mutation.isSuccess
    };
}
