import { i18n } from '@euroland/libs';
import url<PERSON>oin from 'url-join';

export function parseLanguageCode(fullCode: string): string {
  return fullCode.split('-')[0].toLowerCase();
}

export const updateTranslation = async (languageCode: string) => {
  try {
    
    const locale = parseLanguageCode(languageCode);
    const response = await fetch(urlJoin(import.meta.env.BASE_URL, `translations/${locale}.json?v=${import.meta.env.VITE_BUILD_TIME}`), {
      cache: 'force-cache'
    });
    const translations = await response.json();
    await i18n.load(locale, translations, () => {});
  } catch (error) {
    console.error('Error updating translation:', error);
    return;
  }
};