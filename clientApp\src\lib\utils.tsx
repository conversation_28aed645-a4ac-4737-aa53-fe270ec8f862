import { OverviewItem, PressRelease } from "@/types/dashboard";
import { Company } from "@/types/dashboard";
import { i18n } from "@euroland/libs";
import { User, Calendar, ChartColumn } from "lucide-react";
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export interface IStatisticsData {
  numberSearchesLast30Days: number;
  companiesFollowed: {
    numOfFollowThisMonth: number;
    total: number;
  };
  upcomingEvents: {
    upcommingEventsThisMonth: number;
  };
}

export const processCompanyPressReleases = (companies: Company[]): PressRelease[] => {
  const pressReleases = companies.flatMap((company) => 
    (company.pressReleases.nodes || []).map((release) => ({
      ...release,
      company: {
        companyName: company.companyName
      }
    }))
  );

  return pressReleases.sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );
};

export const createOverviewData = (statisticsData: IStatisticsData): OverviewItem[] => {
  const companyFollowed = statisticsData?.companiesFollowed;
  
  return [
    {
      title: i18n.translate("companiesFollowed"),
      description: `${companyFollowed?.numOfFollowThisMonth > 0 
        ? "+" + companyFollowed?.numOfFollowThisMonth 
        : companyFollowed?.numOfFollowThisMonth} this month`,
      count: companyFollowed?.total || 0,
      icon: <User className="text-[#3b82f6] w-[20px] h-[20px]" />,
    },
    {
      title: i18n.translate("upcomingEvents"),
      description: "This month",
      count: statisticsData?.upcomingEvents?.upcommingEventsThisMonth || 0,
      icon: <Calendar className="text-[#a855f7] w-[20px] h-[20px]" />,
    },
    {
      title: i18n.translate("recentSearches"),
      description: "Last 30 days",
      count: statisticsData?.numberSearchesLast30Days || 0,
      icon: <ChartColumn className="text-[#22c55e] w-[20px] h-[20px]" />,
    },
  ];
};