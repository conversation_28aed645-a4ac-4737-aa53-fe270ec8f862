import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.tsx";
import { userManager } from "./configs/oidcConfig.ts";
import { AuthProvider } from "react-oidc-context";
import { BrowserRouter } from "react-router-dom";
import ProtectedApp from "./components/ProtectedApp";
import { updateTranslation } from "./lib/initial-translate";
function getBaseUrl() {
  let baseUrl =  ((document.querySelector('base[href]') || {}) as HTMLBaseElement).href;
  if (baseUrl) {
    baseUrl = baseUrl.substring(location.origin.length);
    //return baseUrl.replace(/\/$/, '');
    return baseUrl;
  }
  return '';
}

(async () => {
  await updateTranslation('en');

  createRoot(document.getElementById("root")!).render(
    <StrictMode>
      <BrowserRouter basename={getBaseUrl()}>
        <AuthProvider
          userManager={userManager}
        >
          <ProtectedApp>
            <App />
          </ProtectedApp>
        </AuthProvider>
      </BrowserRouter>
    </StrictMode>
  );
})();


