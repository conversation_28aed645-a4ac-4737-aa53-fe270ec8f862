import { RecentType } from "@/types/dashboard";
import { Calendar, ChartColumn, ArrowUp, FileText, BriefcaseBusiness } from "lucide-react";
export const RecentIcontype = ({ recentType }: { recentType: RecentType }) => {
  switch (recentType) {
    case RecentType.calendarEvent:
      return (
        <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
          <Calendar className="text-purple-500 w-4 h-4" />
        </div>
      );
    case RecentType.earningsResults:
      return (
        <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
          <ChartColumn className="text-green-500 w-4 h-4" />
        </div>
      );
    case RecentType.pressRelease:
      return (
        <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
          <FileText className="text-blue-500 w-4 h-4" />
        </div>
      );
    case RecentType.sharePriceMovement:
      return (
        <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
          <ArrowUp className="text-green-500 w-4 h-4" />
        </div>
      );
    case RecentType.upcomingEvent:
      return (
        <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
          <Calendar className="text-purple-500 w-4 h-4" />
        </div>
      );
    case RecentType.companyAnnouncement:
      return (
        <div className="flex-shrink-0 w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
          <BriefcaseBusiness className="text-purple-500 w-4 h-4" />
        </div>
      );
    default:
      return null;
  }
};
