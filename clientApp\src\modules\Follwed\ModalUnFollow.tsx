import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useFollowCompanies, useUnFollowCompany } from "@/hooks/useQueries";
import { i18n } from "@euroland/libs";

export const ModalUnFollow = ({
  isOpen,
  onCancel,
  companyCode,
}: {
  isOpen: boolean;
  companyCode: string;
  onCancel: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const { unFollowCompany } = useUnFollowCompany(companyCode);
  const { refetch } = useFollowCompanies();
  const handleUnFollow = async () => {
    onCancel(false);
    await unFollowCompany();
    await refetch();
  };
  return (
    <AlertDialog open={isOpen} onOpenChange={onCancel}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {i18n.translate("unsubscribeFromNordeaBank")}
          </AlertDialogTitle>
          <AlertDialogDescription>
            <p id="radix-«r1v»" className="text-sm text-muted-foreground" />
            <p className="mb-2">
              {i18n.translate("areYouSureYouWantToUnsubscribe")}
            </p>
            <p className="mb-2">By unsubscribing, you will:</p>
            <ul className="list-disc pl-5 space-y-1 mb-2">
              <li>No longer receive price alerts for this company</li>
              <li>No longer receive notifications about press releases</li>
              <li>No longer receive updates about financial events</li>
              <li>No longer see this company on your dashboard</li>
            </ul>
            <p>
              You can always re-subscribe to this company later if you change
              your mind.
            </p>
            <p />
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel className="rounded" onClick={() => onCancel}>
            {i18n.translate("cancel")}
          </AlertDialogCancel>
          <AlertDialogAction
            className="bg-red-600 rounded"
            onClick={handleUnFollow}
          >
            {i18n.translate("unsubscribe")}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
