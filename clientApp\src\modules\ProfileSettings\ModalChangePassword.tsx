import React, { useEffect, useState } from "react";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Loader2, <PERSON>, TriangleAlert } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { i18n } from "@euroland/libs";
import Input from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import type { ChangePasswordResponse } from "@/types/profile";
import { useChangePassword } from "@/hooks/useQueries";

const schema = z
  .object({
    currentPassword: z.string().min(1, "Please enter your current password"),
    newPassword: z
      .string()
      .min(6, "Password must be at least 6 characters long")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[0-9]/, "Password must contain at least one number")
      .regex(
        /[@$!%*?&]/,
        "Password must contain at least one special character"
      ),
    confirmNewPassword: z.string().min(1, "Please confirm your new password"),
  })
  .refine((data) => data.newPassword === data.confirmNewPassword, {
    message: "Passwords do not match",
    path: ["confirmNewPassword"],
  });

type ChangePasswordType = z.infer<typeof schema>;

export const ModalChangePassword = ({
  isOpen,
  onCancel,
}: {
  isOpen: boolean;
  onCancel: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const {
    register,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm<ChangePasswordType>({
    resolver: zodResolver(schema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmNewPassword: "",
    },
  });
  const { changePassword, isLoading, isSuccess } = useChangePassword();
  const { toast } = useToast();
  const [changePasswordReponse, setChangePasswordReponse] =
    useState<ChangePasswordResponse>();

  const onSubmit = async (data: ChangePasswordType) => {
    const result = await changePassword(data);
    setChangePasswordReponse(result as ChangePasswordResponse);
  };

  useEffect(() => {
    if (isSuccess) {
      onCancel(false);
      reset();
      toast({
        variant: "success",
        title: i18n.translate("successfullyUpdatedTheInformation"),
      });
    }
  }, [isSuccess]);

  return (
    <AlertDialog open={isOpen} onOpenChange={onCancel}>
      <AlertDialogContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {i18n.translate("changePassword")}
            </AlertDialogTitle>
          </AlertDialogHeader>
          <div className="pt-6 space-y-4">
            {changePasswordReponse?.httpStatusCode &&
              !isSuccess && (
                <div className="flex items-center gap-1 bg-red-500 text-white rounded p-2 text-sm">
                  <TriangleAlert className="w-4 h-4" />{" "}
                  {changePasswordReponse?.message}
                </div>
              )}
            <div>
              <Input
                type="password"
                {...register("currentPassword")}
                label={i18n.translate("currentPassword")}
                icon={<Lock className="w-4 h-4" />}
                showIconPassWorld
              />
              {errors.currentPassword && (
                <span className="text-red-500 text-sm mt-1 block">
                  {errors.currentPassword.message}
                </span>
              )}
            </div>

            <div>
              <Input
                type="password"
                {...register("newPassword")}
                label={i18n.translate("newPassword")}
                icon={<Lock className="w-4 h-4" />}
                showIconPassWorld
              />
              {errors.newPassword && (
                <span className="text-red-500 text-sm mt-1 block">
                  {errors.newPassword.message}
                </span>
              )}
            </div>

            <div>
              <Input
                type="password"
                {...register("confirmNewPassword")}
                label={i18n.translate("confirmNewPassword")}
                icon={<Lock className="w-4 h-4" />}
                showIconPassWorld
              />
              {errors.confirmNewPassword && (
                <span className="text-red-500 text-sm mt-1 block">
                  {errors.confirmNewPassword.message}
                </span>
              )}
            </div>
          </div>

          <AlertDialogFooter className="mt-6">
            <AlertDialogCancel
              onClick={() => onCancel(false)}
              className="rounded-[6px]"
            >
              {i18n.translate("cancel")}
            </AlertDialogCancel>
            <button
              type="submit"
              onClick={(e) => {
                e.stopPropagation();
              }}
              className="rounded-[6px] bg-euroland-navy px-4 py-2 text-white"
              disabled={isLoading}
            >
              {isLoading ? <Loader2 className="w-4 h-4 animate-spin" /> : i18n.translate("confirmChanges")}
            </button>
          </AlertDialogFooter>
        </form>
      </AlertDialogContent>
    </AlertDialog>
  );
};
