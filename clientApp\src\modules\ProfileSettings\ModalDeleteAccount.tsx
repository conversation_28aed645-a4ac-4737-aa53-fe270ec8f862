import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useAppStore } from "@/stores/useAppStore";
import { i18n } from "@euroland/libs";

export const ModalDeleteAccount = ({
  isOpen,
  onCancel,
}: {
  isOpen: boolean;
  onCancel: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const deleteAllSearchHistories = useAppStore(
    (state) => state.deleteAllSearchHistories
  );

  const clearAllSearchHistories = () => {
    deleteAllSearchHistories();
  };
  return (
    <AlertDialog open={isOpen} onOpenChange={onCancel}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {i18n.translate("warningAccountDeletion")}
          </AlertDialogTitle>
          <AlertDialogDescription>
            <p className="text-sm text-muted-foreground" />
            <p className="text-red-600 font-semibold mb-2">
              This action is irreversible!
            </p>
            <p>Deleting your account will:</p>
            <ul className="list-disc list-inside mb-2">
              <li>Permanently remove all your data</li>
              <li>Cancel all subscriptions</li>
              <li>Remove access to all services</li>
            </ul>
            <p>
              You will not be able to restore any information after proceeding.
            </p>
            <p />
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{i18n.translate("cancel")}</AlertDialogCancel>
          <AlertDialogAction
            onClick={clearAllSearchHistories}
            className="bg-red-600 hover:bg-red-700"
          >
            {i18n.translate("iUnderstandDeleteMyAccount")}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
