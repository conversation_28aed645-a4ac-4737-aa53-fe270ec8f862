import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "@/hooks/use-toast";
import { useDeleteAllSearchHistories } from "@/hooks/useQueries";
import { i18n } from "@euroland/libs";
import { Loader2 } from "lucide-react";

export const ModalClearHistory = ({
  isOpen,
  onCancel,
}: {
  isOpen: boolean;
  onCancel: React.Dispatch<React.SetStateAction<boolean>>;
}) => {

  const { deleteAllSearchHistories, isLoading } = useDeleteAllSearchHistories();

  const clearAllSearchHistories = async () => {
    try {
      await deleteAllSearchHistories();
      toast({
        variant: "success",
        title: i18n.translate("successfullyUpdatedTheInformation"),
      });
    } catch (error) {
      console.error(error);
      toast({
        variant: "destructive",
        title: i18n.translate("failedToUpdateTheInformation"),
      });
    } finally {
      onCancel(false);
    }

  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onCancel}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete your
            entire search history.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={clearAllSearchHistories}>
            {isLoading ? <Loader2 className="animate-spin" /> : "Yes, clear all history"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
