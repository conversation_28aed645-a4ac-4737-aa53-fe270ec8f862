import { userManager } from '@/configs/oidcConfig';
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

// Default configuration without baseURL
const defaultConfig: Omit<AxiosRequestConfig, 'baseURL'> = {
    timeout: 10000,
    headers: {
        'Content-Type': 'application/json',
    },
};

// Variables to be shared across instances
let isRefreshing: boolean = false;
let failedQueue: Array<{
    resolve: () => void;
    reject: (error: unknown) => void;
}> = [];

const processQueue = (error: unknown): void => {
    failedQueue.forEach((prom) => {
        if (error) {
            prom.reject(error);
        } else {
            prom.resolve();
        }
    });
    failedQueue = [];
};

// API client class that accepts baseUrl on initialization
class ApiClient {
    private axiosInstance: AxiosInstance;
    private baseURL: string;
    
    // Static instance for singleton pattern
    private static instance: ApiClient | null = null;

    /**
     * Creates a new ApiClient with the specified baseURL
     * @param baseURL - The base URL for API requests
     */
    constructor(baseURL: string) {
        this.baseURL = baseURL;
        this.axiosInstance = this.createAxiosInstance();
    }

    /**
     * Get single instance of ApiClient (singleton pattern)
     * @param baseURL - Base URL to use when creating instance
     * @returns ApiClient instance
     */
    public static getInstance(baseURL: string = import.meta.env.VITE_API_URL): ApiClient {
        if (!ApiClient.instance) {
            ApiClient.instance = new ApiClient(baseURL);
        }
        return ApiClient.instance;
    }

    /**
     * Creates a new Axios instance
     * @returns Configured Axios instance
     */
    private createAxiosInstance(): AxiosInstance {
        // Create config with baseURL
        const config: AxiosRequestConfig = {
            ...defaultConfig,
            baseURL: this.baseURL,
        };

        const instance: AxiosInstance = axios.create(config);

        // Add request interceptor
        instance.interceptors.request.use(
            async (config: InternalAxiosRequestConfig) => {
                const user = await userManager.getUser();
                const token = user?.access_token;
                config.headers['Authorization'] = `Bearer ${token}`;
                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );

        // Add response interceptor
        instance.interceptors.response.use(
            async (response: AxiosResponse) => {
                return response;
            },
            async (error) => {
                const originalRequest = error.config;

                if (error.response?.status === 401 && !originalRequest._retry) {
                    if (isRefreshing) {
                        // Renew token and return the request
                        return new Promise<void>((resolve, reject) => {
                            failedQueue.push({ resolve, reject });
                        })
                            .then(() => {
                                return instance(originalRequest);
                            })
                            .catch((err) => {
                                return Promise.reject(err);
                            });
                    }

                    // mark the request and set the isRefreshing to true
                    originalRequest._retry = true;
                    isRefreshing = true;

                    try {
                        await userManager.signinSilent();
                        // handle the queue with the new token
                        processQueue(null);
                        return instance(originalRequest); // retry the request
                    } catch (err) {
                        processQueue(err);
                        await userManager.removeUser()
                        return Promise.reject(err);
                    } finally {
                        isRefreshing = false;
                    }
                }

                return Promise.reject(error);
            }
        );

        return instance;
    }

    /**
     * Gets the axios instance to make requests
     * @returns Axios instance
     */
    public getAxiosInstance(): AxiosInstance {
        return this.axiosInstance;
    }
}

// Create default API client instances
const apiClient = new ApiClient(import.meta.env.VITE_API_URL);

// Create second API client with different base URL
const aiSearchApiClient = new ApiClient(import.meta.env.VITE_AI_SEARCH_API_URL);

// Export the axios instances
const axiosInstance = apiClient.getAxiosInstance();
export default axiosInstance;

const aiSearchAxiosInstance = aiSearchApiClient.getAxiosInstance();
export { aiSearchAxiosInstance };



