import { userManager } from '@/configs/oidcConfig';
import { cacheExchange, createClient, fetchExchange } from '@urql/core';

const graphqlURL = import.meta.env.VITE_GRAPHQL_URL || 'https://dev.vn.euroland.com/tools/apigateway/graphql';

const customFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
  const user = await userManager.getUser();
  const token = user?.access_token;

  return fetch(input, {
    ...init,
    credentials: 'include',
    headers: {
      ...(init?.headers || {}),
      Authorization: token ? `Bearer ${token}` : ''
    }
  });
};

export const client = createClient({
  url: graphqlURL,
  exchanges: [cacheExchange, fetchExchange],
  fetch: customFetch,
});