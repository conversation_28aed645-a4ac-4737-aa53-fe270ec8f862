import { INVESTER_DASHBOARD_QUERY, PRESSRELESE_DETAIL_QUERY } from './queries';
import { client } from './client';
import { useQuery } from '@tanstack/react-query';

export function fetchInvesterDashboard() {
  return client.query(INVESTER_DASHBOARD_QUERY, {}).toPromise();
}

export const fetchPressReleaseDetail = (id: string) => {
  return client.query(PRESSRELESE_DETAIL_QUERY(id), {}).toPromise();
}

export function useInvesterDashboard() {
  return useQuery({
    queryKey: ['investerDashboard'],
    queryFn: async () => {
      const response = await fetchInvesterDashboard();
      return response.data;
    },
    staleTime: 600000
  });
}

export const usePressReleaseDetail = (id: string | null) => {
  return useQuery({
    queryKey: ['pressReleaseDetail', id],
    queryFn: async () => {
      if (!id) return null;

      const result = await fetchPressReleaseDetail(id);

      if (result.error) {
        throw new Error(result.error.message);
      }

      return result.data?.pressrelease;
    },
    enabled: !!id,
  });
};