import { gql } from '@urql/core';

export const INVESTER_DASHBOARD_QUERY = gql`
  query UserProfile {
    user {
      userProfile {
        companysubscriptions {
          nodes {
            companyCode
            id
            company {
              companyName
              instruments {
                id
                symbol
                currency {
                  code
                  name
                }
                market {
                  abbreviation
                }
                currentPrice {
                  last
                  open
                  change
                  changePercentage
                }
              }
              pressReleases {
                totalCount
                nodes {
                  companyCode
                  date
                  dateTime
                  hasAttachment
                  id
                  isHidden
                  title
                  languageId
                  messageType {
                    name
                    shortName
                    id
                  }
                  company {
                    companyName
                  }
                }
              }
              fcEvents {
                totalCount
              }
            }
          }
          totalCount
        }
      }
    }
  }
`; 

export const PRESSRELESE_DETAIL_QUERY = (id: string) => gql`query pressreleaseDetailpressreleaseDetail {
  pressrelease(id: ${id}) {
    message
  }
}`