import type { SearchHistoryItem } from "@/types/searchHistory";
import { aiSearchAxiosInstance } from "./apiClient";
import { API_PATHS } from "./apiPath";
import { DEFAULT_LANGUAGE } from "@/constants/appConstants";

export const getSearchHistories = async (): Promise<SearchHistoryItem[]> => {
    const response = await aiSearchAxiosInstance.get<{data: SearchHistoryItem[]}>(API_PATHS.SEARCH.SEARCH_HISTORIES, {
        params: {
            language: DEFAULT_LANGUAGE,
        }
    });

    return response.data.data
};

export const deleteAllSearchHistories = async () => {
    const response = await aiSearchAxiosInstance.delete(API_PATHS.SEARCH.DELETE_ALL);

    return response.data
};

export const deleteSearchHistory = async (historyId: string) => {
    const response = await aiSearchAxiosInstance.delete(API_PATHS.SEARCH.DELETE_SEARCH_HISTORIES + `/${historyId}`);

    return response.data
};