import { StateCreator } from "zustand";
import { TStoreState } from "../useAppStore";
import type { IUserProfile } from "@/types/userComment";

export interface IAppStore {
  userProfile: IUserProfile;
  setUserProfile: (profile: IUserProfile) => void;
}

export const createAppSlice: StateCreator<TStoreState, [], [], IAppStore> = (
  set
) => ({
  userProfile: {} as IUserProfile,
  setUserProfile: (profile) => {
    if (!profile) return;
    set({ userProfile: profile });
  }
});
