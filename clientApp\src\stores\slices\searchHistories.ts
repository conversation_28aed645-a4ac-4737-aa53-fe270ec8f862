

import { StateCreator } from "zustand"
import { TStoreState } from "../useAppStore"
import type { SearchHistoryItem } from "@/types/searchHistory";
import { deleteAllSearchHistories, deleteSearchHistory, getSearchHistories } from "@/services/searchService";

export interface ISearchHistoryStore {
    searchHistories: SearchHistoryItem[];
    historyLoading: {[key: string]: boolean};
    getSearchHistories: () => void;
    deleteSearchHistory: (historyId: string) => void;
    deleteAllSearchHistories: () => void;
}

export const createSearchHistorySlice: StateCreator<
    TStoreState,
    [],
    [],
    ISearchHistoryStore
> = (set,get) => ({
    searchHistories: [],
    historyLoading: {
        'all': false,
    },
    getSearchHistories: async () => {
        const data = await getSearchHistories()
        set({ searchHistories: data })
    },
    deleteSearchHistory: async (historyId) => {
        const {searchHistories} = get()
        await deleteSearchHistory(historyId)
        set({ searchHistories: [...searchHistories].filter((item) => item.id !== historyId) })
    },
    deleteAllSearchHistories: async () => {
        const {historyLoading} = get()
        set({ historyLoading: {...historyLoading, 'all': true} })
        try {   
            await deleteAllSearchHistories()
            set({ searchHistories: [] })
        } catch (error) {
            console.error(error)
        } finally {
            set({ historyLoading: {...historyLoading, 'all': false} })
        }
    },
})