import { create, StateCreator } from 'zustand';
import { createAppSlice, IAppStore } from './slices/appSlice';
import { createSearchHistorySlice, ISearchHistoryStore } from './slices/searchHistories';

export type TStoreState = IAppStore & ISearchHistoryStore

const createRootSlice: StateCreator<TStoreState> = (...a) => ({
  ...createSearchHistorySlice(...a),
  ...createAppSlice(...a),
});

export const useAppStore = create<TStoreState>()((createRootSlice));
