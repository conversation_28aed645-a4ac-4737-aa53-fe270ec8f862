export const enum RecentType {
  upcomingEvent = "Upcoming Event",
  calendarEvent = "Calendar Event",
  pressRelease = "Press Release",
  earningsResults = "Earnings Results",
  sharePriceMovement = "Share Price Movement",
  companyAnnouncement = "Company Announcement",
}

export type OverviewItem = {
  title: string;
  description: string;
  count: number;
  icon: JSX.Element;
}

export type CompanyInstrument = {
  market?: { abbreviation: string };
  currency?: { code: string };
  currentPrice?: {
    last: number;
    changePercentage: number;
  };
}

export type Company = {
  companyName: string;
  pressReleases: {
    nodes: PressRelease[];
  };
  instruments?: CompanyInstrument[];
}

export type PressRelease = {
  id: string;
  title: string;
  date: string;
  messageType?: { name: string };
  company?: {
    companyName: string;
  };
}

export type StatisticsProps = {
  dataOverview: OverviewItem[];
}

export type RecentUpdatesProps = {
  updates: PressRelease[];
}

export type StockPerformanceProps = {
  companies: Company[];
}

export type CompanySubscriptionItem = {
  company: Company;
};