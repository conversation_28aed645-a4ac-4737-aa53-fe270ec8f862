export interface UserComment {
  id: string;
  comment: string;
  highlightedText: string;
  timestamp: string;
  replies: {
    id: string;
    author: string;
    content: string;
    timestamp: string;
  }[];
  company: string;
}

export interface IUserProfile {
  data: IEditDataApi;
  avatar: string;
  email: string;
  fullName: string;
  id: string;
  username: string;
  fullname?: string;
  notificationPreferences: {
    isReceiveBrowserNotifications: boolean;
    isReceiveEmail: boolean;
  };
}

export type IEditDataApi = Partial<
  Pick<IUserProfile, "avatar" | "username">
> & {
  fullName: string;
};

export interface IUploadAvatarResponse {
  url: string;
}
