export const getImageLink = (url: string) => {
    return `${url}`;
}

export const addTimestamp = (url: string) => {
    if (!url) return "";
    const timeStamp = new Date().getTime();
    return url + `?${timeStamp}`;
}

export function removeTimestamp(url: string): string {
    const [base,] = url.split('?');
    return base
}

export function getAvatarInitials(username: string): string {
    if (!username) return "";
    const trimmed = username.trim();
    const words = trimmed.split(/\s+/);
    let initials = "";
    if (words.length > 1) {
        initials = words[0][0] + words[words.length - 1][0];
    } else {
        initials = trimmed.slice(0, 2);
    }
    return initials.toUpperCase();
}