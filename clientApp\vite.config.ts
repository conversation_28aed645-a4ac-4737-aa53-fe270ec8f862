import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// https://vite.dev/config/
export default defineConfig({
  define: {
    "import.meta.env.VITE_BUILD_TIME": JSON.stringify(new Date().getTime()),
  },
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 3005,
    proxy: {
      '/graphql': {
        target: 'http://localhost:5000',
      }
    }
  },
});
