﻿using AutoMapper;
using Euroland.Identity.UserPreferences.Infrastructure.Entities.EurolandIDProfiles;
using Euroland.Identity.UserPreferences.Models.User;

namespace Euroland.Identity.UserPreferences
{
    public class AutoMapperProfile : Profile
    {
        public AutoMapperProfile()
        {
            CreateMap<UserProfileOutputDto, UserEntity>().ReverseMap();
            CreateMap<UPRProfileOutputDto, UserEntity>()
                .ForMember(c => c.IsReceiveEmailNotifications, s => s.MapFrom(m => m.NotificationPreferences.IsReceiveEmail))
                .ForMember(c => c.IsReceiveBrowserNotifications, s => s.MapFrom(m => m.NotificationPreferences.IsReceiveBrowserNotifications))
                .ReverseMap();
        }
    }
}
