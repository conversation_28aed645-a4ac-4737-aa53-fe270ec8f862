﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Euroland.Identity.UserPreferences.Extensions;
using Euroland.Identity.UserPreferences.Models.Dashboard;
using Euroland.Identity.UserPreferences.Services;

namespace Euroland.Identity.UserPreferences.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class DashboardController : Controller
    {
        private readonly IDashboardService _dashboardService;

        public DashboardController(IDashboardService dashboardService)
        {
            _dashboardService = dashboardService;
        }

        [HttpGet("statistical-overview")]
        public async Task<ActionResult<InfoOverviewOutputMode>> StatisticalOverview()
        {
            var userId = TokenExts.GetUserId();
            InfoOverviewOutputMode result = await _dashboardService.StatisticalOverview(Guid.Parse(userId));
            return Ok(result);
        }
    }
}
