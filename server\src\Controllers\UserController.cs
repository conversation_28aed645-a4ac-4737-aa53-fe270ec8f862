﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using Euroland.Identity.UserPreferences.Constants.Enums;
using Euroland.Identity.UserPreferences.Extensions;
using Euroland.Identity.UserPreferences.Infrastructure.Entities.EurolandIDProfiles;
using Euroland.Identity.UserPreferences.Models.Company;
using Euroland.Identity.UserPreferences.Models.User;
using Euroland.Identity.UserPreferences.Services;

namespace Euroland.Identity.UserPreferences.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly IIdentityServerService _identityServerService;
        private readonly IBlobStorageService _blobService;

        public UserController(IUserService userService,
                              IIdentityServerService identityServerService,
                              IBlobStorageService blobService)
        {
            _userService = userService;
            _identityServerService = identityServerService;
            _blobService = blobService;
        }

        [Authorize]
        [HttpGet("profile-by-companycode")]
        public async Task<ActionResult<UserProfileOutputDto>> Profile(string companyCode)
        {
            var userId = TokenExts.GetUserId();
            var result = await _userService.GetProfileAsync(companyCode, Guid.Parse(userId));
            return Ok(result);
        }

        [Authorize]
        [HttpPost("upload-avatar")]
        public async Task<IActionResult> UploadAvatar([FromForm] IFormFile avatar)
        {
            if (avatar == null || avatar.Length == 0)
                return BadRequest("File invalid");

            if (!avatar.ContentType.StartsWith("image/"))
                return BadRequest("Image only");

            var extension = Path.GetExtension(avatar.FileName);
            var userId = TokenExts.GetUserId();
            var fileName = $"{userId}{extension}";
            var url = await _blobService.UploadFileAsync(avatar, fileName);

            return Ok(url);
        }

        [Authorize]
        [HttpGet("profile")]
        public async Task<ActionResult<UPRProfileOutputDto>> UPRProfile()
        {
            var userId = TokenExts.GetUserId();
            UPRProfileOutputDto result = await _userService.GetProfileAsync(Guid.Parse(userId));
            return Ok(result);
        }

        [Authorize]
        [HttpPut("profile")]
        public async Task<ActionResult<UserEntity>> UpdateProfile(UserProfileInputDto userProfile)
        {
            var userId = TokenExts.GetUserId();
            var result = await _userService.UpdateProfile(Guid.Parse(userId), userProfile);
            return Ok(result);
        }

        [Authorize]
        [HttpDelete("profile")]
        public async Task<ActionResult<bool>> Profile()
        {
            var userId = TokenExts.GetUserId();
            bool result = await _userService.DeleteAccount(Guid.Parse(userId));
            return Ok(result);
        }

        [HttpPost("follow-company")]
        public async Task<IActionResult> FollowCompany([Required] string companyCode,
                                                       [Required] UserCompanySubscriptionStatusEnum status)
        {
            var userId = TokenExts.GetUserId();
            bool result = await _userService.FollowCompany(companyCode, Guid.Parse(userId), status);
            return Ok(result);
        }

        [Authorize]
        [HttpGet("following-companies")]
        public async Task<ActionResult<List<FollowingCompany>>> FollowingCompanies(string? language)
        {
            var userId = TokenExts.GetUserId();
            var result = await _userService.GetFollowingCompanies(Guid.Parse(userId), language);
            return Ok(result);
        }

        [Authorize]
        [HttpPost("change-password")]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest model)
        {
            var userName = TokenExts.GetEmail();
            if (!await _identityServerService.VerifyCurrentPassword(userName, model.CurrentPassword))
                return BadRequest("Current password is incorrect");

            var adminToken = await _identityServerService.GetAdminAccessToken();
            var userId = await _identityServerService.GetUserId(userName, adminToken);

            await _identityServerService.ChangePassword(userId, model.NewPassword, adminToken);
            return Ok("Password changed successfully");
        }
    }
}
