﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using Euroland.Identity.UserPreferences.Constants;
using Euroland.Identity.UserPreferences.Filters;
using Euroland.Identity.UserPreferences.Infrastructure;
using Euroland.Identity.UserPreferences.Services;

namespace Euroland.Identity.UserPreferences.Extensions
{
    public static class ProgramExts
    {
        public static IServiceCollection RegisterAPIServices(this IServiceCollection services)
        {
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IDashboardService, DashboardService>();
            services.AddScoped<IGraphQLClientService, GraphQLClientService>();
            services.AddScoped<IIdentityServerService, IdentityServerService>();
            services.AddScoped<IBlobStorageService, BlobStorageService>();

            return services;
        }

        public static IServiceCollection RegisterRepositories(this IServiceCollection services)
        {
            services.Scan(scan => scan
                .FromAssemblies(AppDomain.CurrentDomain.GetAssemblies())
                .AddClasses(classes => classes.AssignableTo(typeof(IRepositoryBase<,,>)))
                .AsImplementedInterfaces()
                .WithScopedLifetime()
            );

            return services;
        }

        public static IServiceCollection AddUnitOfWorks(this IServiceCollection services)
        {
            services.Scan(scan => scan
                .FromAssemblies(AppDomain.CurrentDomain.GetAssemblies())
                .AddClasses(classes => classes.AssignableTo(typeof(IUnitOfWorkBase<>)))
                .AsImplementedInterfaces()
                .WithScopedLifetime()
            );

            return services;
        }

        public static IServiceCollection AddSwaggerService(this IServiceCollection services, Action<SwaggerGenOptions> callback = null)
        {
            services.AddSwaggerGen(c =>
            {
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                {
                    Description = "JWT Authorization header using the Bearer scheme. Example: \"Bearer {token}\"",
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer"
                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement()
                {
                  {
                    new OpenApiSecurityScheme
                    {
                      Reference = new OpenApiReference
                      {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                      },
                      Scheme = "oauth2",
                      Name = "Bearer",
                      In = ParameterLocation.Header,

                    },
                      new List<string>()
                  }
                });
                c.CustomSchemaIds(type => type.ToString().Replace("`", "").Replace("[", "Of").Replace(",", "And").Replace("]", ""));

                c.SchemaFilter<EnumSchemaFilter>();

                if (callback != null)
                {
                    callback(c);
                }
            });

            return services;
        }


        public static IServiceCollection AddHttpClientService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient(HttpClientFactoryNameConst.SDataRequest, client =>
            {
                var wiseBaseUri = configuration.GetSection("SDataGraphQL").Value
                                                        ?? throw new ArgumentNullException("SDataGraphQL");
                client.BaseAddress = new Uri(uriString: wiseBaseUri);
                client.DefaultRequestHeaders.UserAgent.ParseAdd("dotnet-docs");
            });

            services.AddHttpClient(HttpClientFactoryNameConst.KeyCloakRequest, client =>
            {
                var identityServerUri = configuration.GetSection("Keycloak:BaseUrl").Value
                                                        ?? throw new ArgumentNullException("Keycloak:BaseUrl");
                client.BaseAddress = new Uri(uriString: identityServerUri);
                client.DefaultRequestHeaders.UserAgent.ParseAdd("dotnet-docs");
            });

            return services;
        }
    }
}
