﻿using Azure.Core;
using System.Security.Claims;

namespace Euroland.Identity.UserPreferences.Extensions
{
    public class TokenExts
    {
        private static IHttpContextAccessor _httpContextAccessor { get; set; }
        public static void Configure(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public static string? GetUserId()
        {
            if (_httpContextAccessor == null) return null;
            var userClaim = _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier);
            return userClaim != default ? userClaim.Value : null;
        }

        public static string GetEmail()
        {
            if (_httpContextAccessor == null) return null;
            return _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.Email).Value;
        }
        public static string GetGivenName()
        {
            if (_httpContextAccessor == null) return null;
            return _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.GivenName).Value;
        }
        public static string GetFamilyName()
        {
            if (_httpContextAccessor == null) return null;
            return _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.Surname).Value;
        }
        public static string GetFullName()
        {
            if (_httpContextAccessor == null) return null;
            return _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(x => x.Type == "name").Value;
        }

        public static string? GetClientIp()
        {
            string? ip = _httpContextAccessor.HttpContext?.Request.Headers["X-Forwarded-For"];
            if (!string.IsNullOrEmpty(ip))
            {
                return ip.Split(',').First().Trim();
            }
            return _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString();
        }


    }
}
