﻿using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.ComponentModel;
using System.Reflection;

namespace Euroland.Identity.UserPreferences.Filters
{
    public class EnumSchemaFilter : ISchemaFilter
    {
        public void Apply(OpenApiSchema schema, SchemaFilterContext context)
        {
            if (context.Type.IsEnum)
            {
                schema.Enum.Clear();
                schema.Description = "Options:\n\n";

                foreach (var enumValue in Enum.GetValues(context.Type))
                {
                    var intValue = Convert.ToInt32(enumValue);
                    var name = enumValue.ToString();

                    var description = enumValue.GetType()
                        .GetField(name)?
                        .GetCustomAttribute<DescriptionAttribute>()?.Description ?? name;

                    schema.Description += $"**{intValue}** - {description}\n";

                    schema.Enum.Add(new OpenApiInteger(intValue));
                }
            }
        }
    }
}
