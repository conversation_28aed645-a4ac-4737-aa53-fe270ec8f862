﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Euroland.Identity.UserPreferences.Constants.Enums;

namespace Euroland.Identity.UserPreferences.Infrastructure.Entities.EurolandIDProfiles;

[Table("UserCompanySubscriptions")]
public partial class UserCompanySubscriptionEntity
{
    [Key]
    public Guid Id { get; set; }

    [StringLength(50)]
    [Unicode(false)]
    public string CompanyCode { get; set; } = null!;

    public Guid UserId { get; set; }

    public DateTime LastModifyDate { get; set; }

    /// <summary>
    /// Following = 1,
    /// Unfollow = 2,
    /// Decline = 3
    /// </summary>
    public UserCompanySubscriptionStatusEnum Status { get; set; }
}
