﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Euroland.Identity.UserPreferences.Infrastructure.Entities.EurolandIDProfiles;

[Table("Users")]
public partial class UserEntity
{
    [Key]
    public Guid Id { get; set; }

    [StringLength(255)]
    public string? Username { get; set; }

    [StringLength(255)]
    public string? Fullname { get; set; }

    [StringLength(255)]
    [Unicode(false)]
    public string Email { get; set; } = null!;

    [StringLength(255)]
    [Unicode(false)]
    public string? Avatar { get; set; }

    [StringLength(10)]
    [Unicode(false)]
    public string UserLanguage { get; set; } = null!;

    public bool IsReceiveEmailNotifications { get; set; }
    public bool IsReceiveBrowserNotifications { get; set; }
}
