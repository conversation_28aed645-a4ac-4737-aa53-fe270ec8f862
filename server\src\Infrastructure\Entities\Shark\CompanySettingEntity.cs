﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System.ComponentModel.DataAnnotations;
using Microsoft.EntityFrameworkCore;

namespace Euroland.Identity.UserPreferences.Infrastructure.Entities.Shark;

public partial class CompanySettingEntity
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(10)]
    [Unicode(false)]
    public string CompanyCode { get; set; }

    [StringLength(250)]
    [Unicode(false)]
    public string CompanyLogo { get; set; }

    [StringLength(250)]
    [Unicode(false)]
    public string EnabledLanguages { get; set; }

    [StringLength(10)]
    [Unicode(false)]
    public string DefaultLanguage { get; set; }

    [StringLength(250)]
    [Unicode(false)]
    public string Timezone { get; set; }

    public bool? UseLocalTimezone { get; set; }

    [StringLength(250)]
    [Unicode(false)]
    public string Industry { get; set; }
}