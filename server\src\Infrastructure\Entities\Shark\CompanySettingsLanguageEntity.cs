﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace Euroland.Identity.UserPreferences.Infrastructure.Entities.Shark;

[Table("CompanySettingsLanguage")]
public partial class CompanySettingsLanguageEntity
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(10)]
    [Unicode(false)]
    public string CompanyCode { get; set; }

    [Required]
    [StringLength(10)]
    [Unicode(false)]
    public string Language { get; set; }

    [Required]
    [StringLength(250)]
    public string Name { get; set; }

    [StringLength(250)]
    public string HomePage { get; set; }

    [StringLength(250)]
    public string EmailSender { get; set; }

    [StringLength(250)]
    public string EmailSenderName { get; set; }
}