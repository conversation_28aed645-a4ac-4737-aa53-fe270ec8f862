﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Euroland.Identity.UserPreferences.Infrastructure.Entities.EurolandIDProfiles;

namespace Euroland.Identity.UserPreferences.Infrastructure;

public partial class EurolandIDProfileDbContext : DbContext
{
    public EurolandIDProfileDbContext()
    {
    }

    public EurolandIDProfileDbContext(DbContextOptions<EurolandIDProfileDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<UserEntity> Users { get; set; }

    public virtual DbSet<UserCompanySubscriptionEntity> UserCompanySubscriptions { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<UserEntity>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__UPr_User__3214EC0780E3CECD");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<UserCompanySubscriptionEntity>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__UPR_User__3214EC077190B58B");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Status).HasComment("Following = 1,\r\nUnfollow = 2,\r\nDecline = 3");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
