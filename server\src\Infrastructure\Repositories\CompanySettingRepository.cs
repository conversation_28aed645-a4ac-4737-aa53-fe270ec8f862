﻿

using Euroland.Identity.UserPreferences.Infrastructure.Entities.Shark;
using Microsoft.EntityFrameworkCore;

namespace Euroland.Identity.UserPreferences.Infrastructure.Repositories
{
    public interface ICompanySettingRepository : IRepositoryBase<SharkDbContext, CompanySettingEntity, int>
    {
        IQueryable<CompanySettingEntity> GetAllByCompanyCodes(List<string> companyCodes);
        Task<CompanySettingEntity?> GetByCompanyCodeAsync(string companyCode);
    }

    public class CompanySettingRepository : RepositoryBase<SharkDbContext, CompanySettingEntity, int>, ICompanySettingRepository
    {
        public CompanySettingRepository(SharkDbContext context) : base(context)
        {
        }

        public IQueryable<CompanySettingEntity> GetAllByCompanyCodes(List<string> companyCodes)
        {
            return _dbContext.CompanySettings.Where(s => companyCodes.Contains(s.CompanyCode));
        }

        public async Task<CompanySettingEntity?> GetByCompanyCodeAsync(string companyCode)
        {
            return await _dbContext.CompanySettings.FirstOrDefaultAsync(s => s.CompanyCode == companyCode);
        }
    }
}
