﻿using Microsoft.EntityFrameworkCore;
using Euroland.Identity.UserPreferences.Infrastructure.Entities.Shark;

namespace Euroland.Identity.UserPreferences.Infrastructure.Repositories
{
    public interface ICompanySettingsLanguageRepository : IRepositoryBase<SharkDbContext, CompanySettingsLanguageEntity, int>
    {
        IQueryable<CompanySettingsLanguageEntity> GetAllByCompanyCodes(List<string> companyCodes, string? userLanguage);
    }

    public class CompanySettingsLanguageRepository : RepositoryBase<SharkDbContext, CompanySettingsLanguageEntity, int>, ICompanySettingsLanguageRepository
    {
        public CompanySettingsLanguageRepository(SharkDbContext context) : base(context)
        {
        }

        public IQueryable<CompanySettingsLanguageEntity> GetAllByCompanyCodes(List<string> companyCodes, string? userLanguage)
        {
            var query = _dbContext.CompanySettingsLanguages
                                  .AsNoTracking()
                                  .Where(s => companyCodes.Contains(s.CompanyCode)
                                                && s.Language == userLanguage);
            return query;
        }
    }
}
