﻿using Microsoft.EntityFrameworkCore;
using Euroland.Identity.UserPreferences.Constants.Enums;
using Euroland.Identity.UserPreferences.Infrastructure.Entities.EurolandIDProfiles;

namespace Euroland.Identity.UserPreferences.Infrastructure.Repositories
{
    public interface IUserCompanySubscriptionRepository : IRepositoryBase<EurolandIDProfileDbContext, UserCompanySubscriptionEntity, Guid>
    {
        Task<UserCompanySubscriptionEntity?> GetSubscription(Guid userId, string companyCode);
        IQueryable<UserCompanySubscriptionEntity> GetQueryableSubscriptionByUserId(Guid userId, UserCompanySubscriptionStatusEnum? status = null);
        IQueryable<UserCompanySubscriptionEntity> GetSubscriptionByUserId(Guid id);
    }

    public class UserCompanySubscriptionRepository : RepositoryBase<EurolandIDProfileDbContext, UserCompanySubscriptionEntity, Guid>, IUserCompanySubscriptionRepository
    {
        public UserCompanySubscriptionRepository(EurolandIDProfileDbContext context) : base(context)
        {
        }

        public async Task<UserCompanySubscriptionEntity?> GetSubscription(Guid userId, string companyCode)
        {
            var query = await _dbContext.UserCompanySubscriptions.FirstOrDefaultAsync(s => s.UserId == userId && s.CompanyCode == companyCode);
            return query;
        }

        public IQueryable<UserCompanySubscriptionEntity> GetQueryableSubscriptionByUserId(Guid userId, UserCompanySubscriptionStatusEnum? status = null)
        {
            var query = _dbContext.UserCompanySubscriptions
                                  .Where(s => s.UserId == userId && (!status.HasValue || status == s.Status))
                                  .AsNoTracking();
            return query;
        }

        public IQueryable<UserCompanySubscriptionEntity> GetSubscriptionByUserId(Guid id)
        {
            var query = _dbContext.UserCompanySubscriptions.Where(s => s.UserId == id).AsQueryable();
            return query;
        }
    }
}
