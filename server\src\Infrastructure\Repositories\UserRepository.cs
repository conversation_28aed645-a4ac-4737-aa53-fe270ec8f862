﻿using Euroland.Identity.UserPreferences.Infrastructure.Entities.EurolandIDProfiles;

namespace Euroland.Identity.UserPreferences.Infrastructure.Repositories
{
    public interface IUserRepository : IRepositoryBase<EurolandIDProfileDbContext, UserEntity, Guid>
    {

    }

    public class UserRepository : RepositoryBase<EurolandIDProfileDbContext, UserEntity, Guid>, IUserRepository
    {
        public UserRepository(EurolandIDProfileDbContext context) : base(context)
        {
        }
    }
}
