﻿using Microsoft.EntityFrameworkCore;

namespace Euroland.Identity.UserPreferences.Infrastructure
{
    public interface IRepositoryBase<TDbContext, TEntity, TKeyType> where TEntity : class where TDbContext : DbContext
    {
        Task<TEntity?> GetByKeyAsync(TKeyType id);
        Task<IEnumerable<TEntity>> GetAllAsync(bool isNoTracking = false);
        void Add(TEntity entity);
        Task AddAsync(TEntity entity);
        void Update(TEntity entity);
        void Remove(TEntity entity);
    }

    public abstract class RepositoryBase<TDbContext, TEntity, TKeyType> : IRepositoryBase<TDbContext, TEntity, TKeyType> where TEntity : class where TDbContext : DbContext
    {
        protected readonly TDbContext _dbContext;

        public RepositoryBase(TDbContext context)
        {
            _dbContext = context;
        }

        public virtual async Task<TEntity?> GetByKeyAsync(TKeyType id)
        {
            return await _dbContext.Set<TEntity>().FindAsync(id);
        }

        public virtual async Task<IEnumerable<TEntity>> GetAllAsync(bool isNoTracking = false)
        {
            var query = _dbContext.Set<TEntity>().AsQueryable();
            if (isNoTracking)
            {
                query = query.AsNoTracking();
            }
            return await query.ToListAsync();
        }

        public virtual void Add(TEntity entity)
        {
            _dbContext.Set<TEntity>().Add(entity);
        }

        public virtual void Update(TEntity entity)
        {
            _dbContext.Set<TEntity>().Update(entity);
        }

        public virtual void Remove(TEntity entity)
        {
            _dbContext.Set<TEntity>().Remove(entity);
        }

        public virtual async Task AddAsync(TEntity entity)
        {
            await _dbContext.Set<TEntity>().AddAsync(entity);
        }
    }
}
