﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using Microsoft.EntityFrameworkCore;
using Euroland.Identity.UserPreferences.Infrastructure.Entities.Shark;

namespace Euroland.Identity.UserPreferences.Infrastructure;

public partial class SharkDbContext : DbContext
{
    public SharkDbContext(DbContextOptions<SharkDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<CompanySettingEntity> CompanySettings { get; set; }

    public virtual DbSet<CompanySettingsLanguageEntity> CompanySettingsLanguages { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<CompanySettingEntity>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__CompanyS__11A0134A3E363115");
        });

        modelBuilder.Entity<CompanySettingsLanguageEntity>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__CompanyN__3214EC070C8F3529");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}