﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Euroland.Identity.UserPreferences.Infrastructure.Repositories;

namespace Euroland.Identity.UserPreferences.Infrastructure
{
    public interface IUnitOfWorkBase<TDbContext> where TDbContext : DbContext
    {
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        void RollbackTransaction();
        void Dispose();
    }

    public class UnitOfWorkBase<TDbContext>(TDbContext dbContext
        ) : IUnitOfWorkBase<TDbContext> where TDbContext : DbContext
    {
        private readonly TDbContext _dbContext = dbContext;
        private IDbContextTransaction _transaction;

        public TDbContext DbContext => _dbContext;


        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            return await _dbContext.SaveChangesAsync(cancellationToken);
        }

        public async Task BeginTransactionAsync()
        {
            _transaction = await _dbContext.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            await _transaction.CommitAsync();
            _transaction.Dispose();
        }

        public void RollbackTransaction()
        {
            _transaction.Rollback();
            _transaction.Dispose();
        }

        public void Dispose()
        {
            _dbContext.Dispose();
        }
    }
}
