﻿using Euroland.Identity.UserPreferences.Infrastructure.Repositories;

namespace Euroland.Identity.UserPreferences.Infrastructure.UnitOfWorks
{
    public interface IEurolandIDProfileUnitOfWork : IUnitOfWorkBase<EurolandIDProfileDbContext>
    {
        IUserCompanySubscriptionRepository UserCompanySubscriptionRepository { get; }
        IUserRepository UserRepository { get; }
    }

    public class EurolandIDProfileUnitOfWork(EurolandIDProfileDbContext dbContext,
                        IUserCompanySubscriptionRepository userCompanySubscriptionRepository,
                        IUserRepository userRepository)
                : UnitOfWorkBase<EurolandIDProfileDbContext>(dbContext), IEurolandIDProfileUnitOfWork
    {
        public IUserCompanySubscriptionRepository UserCompanySubscriptionRepository => userCompanySubscriptionRepository;
        public IUserRepository UserRepository => userRepository;
    }
}
