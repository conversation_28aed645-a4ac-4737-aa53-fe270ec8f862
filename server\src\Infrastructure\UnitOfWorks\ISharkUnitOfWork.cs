﻿using Euroland.Identity.UserPreferences.Infrastructure.Repositories;

namespace Euroland.Identity.UserPreferences.Infrastructure.UnitOfWorks
{
    public interface ISharkUnitOfWork : IUnitOfWorkBase<SharkDbContext>
    {
        ICompanySettingRepository CompanySettingRepository { get; }
        ICompanySettingsLanguageRepository CompanySettingLanguageRepository { get; }
    }

    public class SharkUnitOfWork(SharkDbContext context,
                            ICompanySettingRepository companySettingRepository,
                            ICompanySettingsLanguageRepository companyNameTranslationSettingRepository)
        : UnitOfWorkBase<SharkDbContext>(context), ISharkUnitOfWork
    {
        public ICompanySettingRepository CompanySettingRepository => companySettingRepository;
        public ICompanySettingsLanguageRepository CompanySettingLanguageRepository => companyNameTranslationSettingRepository;
    }
}
