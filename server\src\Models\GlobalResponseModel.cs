﻿using System.Text.Json.Serialization;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Euroland.Identity.UserPreferences.Models
{
    public class GlobalResponseModel
    {
        public GlobalResponseModel()
        {
        }

        public GlobalResponseModel(string requestUrl,
                                   int httpStatusCode)
        {
            RequestUrl = requestUrl ?? throw new ArgumentNullException(nameof(requestUrl));
            HttpStatusCode = httpStatusCode;
        }

        public GlobalResponseModel(string requestUrl,
                                   object? data,
                                   string? error,
                                   bool status,
                                   int httpStatusCode,
                                   string? message)
        {
            RequestUrl = requestUrl ?? throw new ArgumentNullException(nameof(requestUrl));
            Error = error;
            IsSuccess = status;
            HttpStatusCode = httpStatusCode;
            Message = message;
            Data = data;
        }

        [JsonPropertyName("requestUrl")]
        public string RequestUrl { get; set; }
        [JsonPropertyName("error")]
        public string? Error { get; set; }
        [JsonPropertyName("isSuccess")]
        public bool IsSuccess { get; set; }
        [JsonPropertyName("httpStatusCode")]
        public int? HttpStatusCode { get; set; }
        [JsonPropertyName("message")]
        public string? Message { get; set; }
        [JsonPropertyName("data")]
        public object? Data { get; set; }
    }
}
