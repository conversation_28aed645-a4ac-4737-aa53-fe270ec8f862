﻿namespace Euroland.Identity.UserPreferences.Models.GraphQL
{
    public class MultiCompanyFinancialEventsResponse
    {
        public Dictionary<string, Company> Data { get; set; }
    }

    public class Company
    {
        public Fundamental Fundamental { get; set; }
    }

    public class Fundamental
    {
        public FinancialEvents FinancialEvents { get; set; }
    }

    public class FinancialEvents
    {
        public EventNode[] Nodes { get; set; }
    }

    public class EventNode
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int EventTypeId { get; set; }
        public int EventId { get; set; }
        public string Title { get; set; }
    }
}
