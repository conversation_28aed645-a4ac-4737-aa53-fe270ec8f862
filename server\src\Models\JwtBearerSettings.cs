﻿namespace Euroland.Identity.UserPreferences.Models
{
    public class JwtBearerSettings
    {
        public string MetadataAddress { get; set; } = string.Empty;
        public bool RequireHttpsMetadata { get; set; } = true;
        public string Authority { get; set; } = string.Empty;
        public string ValidIssuer { get; set; } = string.Empty;
        public string ValidAudience { get; set; } = string.Empty;
        public int ClockSkewSeconds { get; set; } = 0;
    }
}
