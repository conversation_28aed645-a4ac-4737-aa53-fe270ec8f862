﻿using Euroland.Identity.UserPreferences.Constants.Enums;

namespace Euroland.Identity.UserPreferences.Models.User
{
    public class UserProfileOutputDto
    {
        public Guid Id { get; set; }
        public string? Username { get; set; }
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public string? Avatar { get; set; }

        public UserCompanySubscriptionStatusEnum CurrentFollowingStatus { get; set; }
    }

    public class UPRProfileOutputDto
    {
        public Guid Id { get; set; }
        public string? Username { get; set; }
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public string? Avatar { get; set; }
        public NotificationPreferencesSettingProfile? NotificationPreferences {  get; set; }
    }
}
