using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using Serilog;
using System.Text;
using Euroland.Identity.UserPreferences;
using Euroland.Identity.UserPreferences.Extensions;
using Euroland.Identity.UserPreferences.Infrastructure;
using Euroland.Identity.UserPreferences.Middlewares;
using Euroland.Identity.UserPreferences.Models;

var builder = WebApplication.CreateBuilder(args);

builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables();

var logger = new LoggerConfiguration()
          .WriteTo.Console()
          .WriteTo.File("logs/log.txt", rollingInterval: RollingInterval.Day)
          .ReadFrom.Configuration(builder.Configuration)
          .CreateLogger();


//builder.Logging.ClearProviders();
builder.Logging.AddSerilog(logger);

// Add services to the container.
builder.Services.AddDbContext<SharkDbContext>(options =>
                                                   options.UseSqlServer(builder.Configuration.GetConnectionString("SharkDbConnection")));

builder.Services.AddDbContext<EurolandIDProfileDbContext>(options =>
                                                   options.UseSqlServer(builder.Configuration.GetConnectionString("EurolandIDProfileConnection")));

// Add API service here
builder.Services.RegisterAPIServices();
builder.Services.RegisterRepositories();
builder.Services.AddUnitOfWorks();
builder.Services.AddControllers();

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerService();

builder.Services.AddHttpContextAccessor();
builder.Services.AddHttpClientService(builder.Configuration);

builder.Services.AddAutoMapper(typeof(AutoMapperProfile).Assembly);

builder.Services.AddHealthChecks();

var configuration = builder.Configuration;

builder.Services.Configure<JwtBearerSettings>(builder.Configuration.GetSection("Authentication:Keycloak"));
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = "keycloak";
    options.DefaultChallengeScheme = "keycloak";
    options.DefaultScheme = "keycloak";
})
.AddJwtBearer("keycloak", options =>
{
    var serviceProvider = builder.Services.BuildServiceProvider();
    var jwtSettings = serviceProvider.GetRequiredService<IOptions<JwtBearerSettings>>().Value;

    options.IncludeErrorDetails = true;
    options.RefreshOnIssuerKeyNotFound = true;
    options.SaveToken = true;
    options.MetadataAddress = jwtSettings.MetadataAddress;
    options.RequireHttpsMetadata = jwtSettings.RequireHttpsMetadata;
    options.Authority = jwtSettings.Authority;

    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidIssuer = jwtSettings.ValidIssuer,
        ValidateAudience = true,
        ValidAudience = jwtSettings.ValidAudience,
        ValidateIssuerSigningKey = true,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.FromSeconds(jwtSettings.ClockSkewSeconds)
    };
});

// Add CORS services
var allowedOrigins = builder.Configuration.GetSection("AllowedOrigins").Get<string[]>()
                     ?? throw new ArgumentNullException("AllowedOrigins");
builder.Services.AddCors(options =>
{
    options.AddPolicy("CorsPolicy", policyBuilder =>
    {
        policyBuilder
            .WithOrigins(allowedOrigins)
            .AllowAnyMethod()
            .AllowAnyHeader();
    });
});

builder.Services.Configure<SecurityStampValidatorOptions>(options =>
{
    options.ValidationInterval = TimeSpan.FromMinutes(1);
});

var app = builder.Build();
var env = builder.Environment;

if (!env.IsProduction() && !env.IsEnvironment("Gamma"))
{
    app.UseSwagger();
    app.UseSwaggerUI();
}


// Use CORS middleware before other middleware
app.UseCors("CorsPolicy");
app.UseStaticFiles();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.MapHealthChecks("/health");
app.UseMiddleware<GlobalResponseMiddleware>();
TokenExts.Configure(app.Services.GetRequiredService<IHttpContextAccessor>());
app.Run();
