﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;

namespace Euroland.Identity.UserPreferences.Services
{
    public interface IBlobStorageService
    {
        Task<string> UploadFileAsync(IFormFile file, string fileName);
    }

    public class BlobStorageService : IBlobStorageService
    {
        private readonly BlobContainerClient _containerClient;

        public BlobStorageService(IConfiguration configuration)
        {
            var connectionString = configuration["Azure:BlobStorage:ConnectionString"];
            var containerName = configuration["Azure:BlobStorage:ContainerName"];

            var blobServiceClient = new BlobServiceClient(connectionString);
            _containerClient = blobServiceClient.GetBlobContainerClient(containerName);
        }

        public async Task<string> UploadFileAsync(IFormFile file, string fileName)
        {
            await _containerClient.CreateIfNotExistsAsync();

            var blobClient = _containerClient.GetBlobClient(fileName);

            var headers = new BlobHttpHeaders
            {
                ContentType = file.ContentType
            };

            using var stream = file.OpenReadStream();
            await blobClient.UploadAsync(
             stream, new BlobUploadOptions
             {
                 HttpHeaders = new BlobHttpHeaders
                 {
                     ContentType = file.ContentType
                 }
             });

            return blobClient.Uri.ToString();
        }
    }
}
