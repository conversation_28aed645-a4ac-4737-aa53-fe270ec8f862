﻿using Microsoft.EntityFrameworkCore;
using Euroland.Identity.UserPreferences.Extensions;
using Euroland.Identity.UserPreferences.Infrastructure.UnitOfWorks;
using Euroland.Identity.UserPreferences.Models.Dashboard;

namespace Euroland.Identity.UserPreferences.Services
{
    public interface IDashboardService
    {
        Task<InfoOverviewOutputMode> StatisticalOverview(Guid userId);
    }

    public class DashboardService : IDashboardService
    {
        private readonly IEurolandIDProfileUnitOfWork _uprUnitOfWork;
        private readonly IGraphQLClientService _graphQLClientService;

        public DashboardService(
                                IGraphQLClientService graphQLClientService,
                                IEurolandIDProfileUnitOfWork uprUnitOfWork)
        {
            _graphQLClientService = graphQLClientService;
            _uprUnitOfWork = uprUnitOfWork;
        }

        public async Task<InfoOverviewOutputMode> StatisticalOverview(Guid userId)
        {
            var utcNow = DatetimeExts.DateTimeUtcNow();
            var firstDateOfMonth = new DateTime(utcNow.Year, utcNow.Month, 1);

            var followedCompanies = _uprUnitOfWork.UserCompanySubscriptionRepository.GetQueryableSubscriptionByUserId(userId,
                Constants.Enums.UserCompanySubscriptionStatusEnum.Following);

            var numOfFollowThisMonth = await followedCompanies.Where(s => s.LastModifyDate >= firstDateOfMonth
                                                                    && s.LastModifyDate <= utcNow).CountAsync();

            var companyCodes = await followedCompanies.Select(s => s.CompanyCode).ToListAsync();
            var aMonthAgo = utcNow.AddMonths(-1);

            var upCommingEventsThisMonth = await _graphQLClientService.GetFinancialEventsAsync(companyCodes,
                                                                                               aMonthAgo.ToString("yyyy-MM-ddT00:00:00.000Z"),
                                                                                               utcNow.ToString("yyyy-MM-ddT23:59:59.000Z"));

            var numUpcommingEvent = upCommingEventsThisMonth.Data.SelectMany(s => s.Value.Fundamental.FinancialEvents.Nodes).Count();

            var result = new InfoOverviewOutputMode
            {
                CompaniesFollowed = new CompaniesFollowedOverviewModel
                {
                    Total = followedCompanies.Count(),
                    NumOfFollowThisMonth = numOfFollowThisMonth
                },
                UpcommingEventsThisMonth = numUpcommingEvent,
                NumberSearchesLast30Days = 14
            };
            return result;
        }
    }
}
