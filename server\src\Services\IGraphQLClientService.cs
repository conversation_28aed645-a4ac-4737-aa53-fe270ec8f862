﻿using System.Text.Json;
using System.Text;
using Euroland.Identity.UserPreferences.Models.GraphQL;
using Euroland.Identity.UserPreferences.Constants;

namespace Euroland.Identity.UserPreferences.Services
{
    public interface IGraphQLClientService
    {
        Task<MultiCompanyFinancialEventsResponse?> GetFinancialEventsAsync(List<string> companyCodes, string startDate, string endDate);
    }

    public class GraphQLClientService : IGraphQLClientService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly string _sDataGraphQLUrl;
        private readonly IConfiguration _configuration;
        private readonly ILogger<GraphQLClientService> _logger;

        public GraphQLClientService(IHttpClientFactory httpClientFactory,
                                    IConfiguration configuration,
                                    ILogger<GraphQLClientService> logger)
        {
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _sDataGraphQLUrl = _configuration.GetValue<string>("SDataGraphQL") ?? throw new ArgumentNullException(nameof(_sDataGraphQLUrl));
            _logger = logger;
        }

        public async Task<MultiCompanyFinancialEventsResponse?> GetFinancialEventsAsync(List<string> companyCodes, string startDate, string endDate)
        {
            var queryParts = new List<string>();


            for (int i = 0; i < companyCodes.Count; i++)
            {
                string alias = $"company{i + 1}";
                queryParts.Add($@"
                {alias}: company(code: ""{companyCodes[i]}"") {{
                    code
                    fundamental {{
                        financialEvents(
                            where: {{ and: {{ startDate: {{ gte: ""{startDate}"", lte: ""{endDate}"" }} }} }}
                        ) {{
                            nodes {{
                                startDate
                                endDate
                                eventTypeId
                                eventId
                                title
                            }}
                        }}
                    }}
                }}");
            }

            if (!queryParts.Any())
            {
                return null;
            }

            string query = $"query {{ {string.Join("\n", queryParts)} }}";

            _logger.LogInformation(query);

            var requestBody = JsonSerializer.Serialize(new { query });
            var content = new StringContent(requestBody, Encoding.UTF8, "application/json");

            using var client = _httpClientFactory.CreateClient(HttpClientFactoryNameConst.SDataRequest);
            var response = await client.PostAsync("", content);

            if (!response.IsSuccessStatusCode)
            {
                throw new InvalidDataException($"Request failed with status {response.StatusCode}");
            }

            var responseBody = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<MultiCompanyFinancialEventsResponse>(responseBody,
                                                                                         new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            return result;
        }
    }
}
