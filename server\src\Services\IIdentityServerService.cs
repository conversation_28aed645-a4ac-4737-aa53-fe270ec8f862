﻿using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Euroland.Identity.UserPreferences.Constants;

namespace Euroland.Identity.UserPreferences.Services
{
    public interface IIdentityServerService
    {
        Task<bool> VerifyCurrentPassword(string username, string currentPassword);
        Task<string> GetAdminAccessToken();
        Task<string> GetUserId(string username, string adminToken);
        Task ChangePassword(string userId, string newPassword, string adminToken);
    }

    public class IdentityServerService : IIdentityServerService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _config;

        public IdentityServerService(IHttpClientFactory httpClientFactory, IConfiguration config)
        {
            _httpClient = httpClientFactory.CreateClient(HttpClientFactoryNameConst.KeyCloakRequest);
            _config = config;
        }

        public async Task<bool> VerifyCurrentPassword(string username, string currentPassword)
        {
            var realm = _config["Keycloak:Realm"];
            var clientId = _config["Keycloak:ClientId"];

            var content = new FormUrlEncodedContent(new[]
            {
            new KeyValuePair<string, string>("grant_type", "password"),
            new KeyValuePair<string, string>("client_id", clientId),
            new KeyValuePair<string, string>("username", username),
            new KeyValuePair<string, string>("password", currentPassword),
        });

            var response = await _httpClient.PostAsync(
                $"{_config["Keycloak:BaseUrl"]}/realms/{realm}/protocol/openid-connect/token", content);

            return response.IsSuccessStatusCode;
        }

        public async Task<string> GetAdminAccessToken()
        {
            var realm = _config["Keycloak:Realm"];
            var clientId = _config["Keycloak:AdminClientId"];
            var clientSecret = _config["Keycloak:AdminClientSecret"];

            var content = new FormUrlEncodedContent(new[]
            {
            new KeyValuePair<string, string>("grant_type", "client_credentials"),
            new KeyValuePair<string, string>("client_id", clientId),
            new KeyValuePair<string, string>("client_secret", clientSecret)
        });

            var response = await _httpClient.PostAsync(
                $"{_config["Keycloak:BaseUrl"]}/realms/{realm}/protocol/openid-connect/token", content);

            var result = await response.Content.ReadFromJsonAsync<JsonElement>();

            if (result.TryGetProperty("error", out JsonElement error))
            {
                throw new InvalidDataException($"{error.GetString()} - {result.GetProperty("error_description").GetString()}");
            }

            return result.GetProperty("access_token").GetString();
        }

        public async Task<string> GetUserId(string username, string adminToken)
        {
            var realm = _config["Keycloak:Realm"];
            var req = new HttpRequestMessage(HttpMethod.Get,
                $"{_config["Keycloak:BaseUrl"]}/admin/realms/{realm}/users?username={username}");
            req.Headers.Authorization = new AuthenticationHeaderValue("Bearer", adminToken);

            var res = await _httpClient.SendAsync(req);
            if (!res.IsSuccessStatusCode)
            {
                throw new InvalidDataException($"Response from Identity Server: {res.StatusCode.GetHashCode()} - {res.ReasonPhrase}");
            }

            var users = await res.Content.ReadFromJsonAsync<JsonElement>();
            return users[0].GetProperty("id").GetString();
        }

        public async Task ChangePassword(string userId, string newPassword, string adminToken)
        {
            var realm = _config["Keycloak:Realm"];
            var url = $"{_config["Keycloak:BaseUrl"]}/admin/realms/{realm}/users/{userId}/reset-password";

            var request = new HttpRequestMessage(HttpMethod.Put, url);
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", adminToken);

            request.Content = JsonContent.Create(new
            {
                type = "password",
                value = newPassword,
                temporary = false
            });

            var response = await _httpClient.SendAsync(request);
            response.EnsureSuccessStatusCode();
        }
    }
}
