﻿using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Euroland.Identity.UserPreferences.Constants.Enums;
using Euroland.Identity.UserPreferences.Extensions;
using Euroland.Identity.UserPreferences.Infrastructure;
using Euroland.Identity.UserPreferences.Infrastructure.Entities.EurolandIDProfiles;
using Euroland.Identity.UserPreferences.Infrastructure.Entities.Shark;
using Euroland.Identity.UserPreferences.Infrastructure.UnitOfWorks;
using Euroland.Identity.UserPreferences.Models.Company;
using Euroland.Identity.UserPreferences.Models.User;

namespace Euroland.Identity.UserPreferences.Services
{
    public interface IUserService
    {
        Task<bool> FollowCompany(string companyCode, Guid userId, UserCompanySubscriptionStatusEnum status);
        Task<UserProfileOutputDto> GetProfileAsync(string companyCode, Guid userId);
        Task<List<FollowingCompany>> GetFollowingCompanies(Guid userId, string? language);
        Task<UserEntity> UpdateProfile(Guid userId, UserProfileInputDto userProfile);
        Task<bool> DeleteAccount(Guid guid);
        Task<UPRProfileOutputDto> GetProfileAsync(Guid guid);
    }

    public class UserService : IUserService
    {
        private readonly IMapper _mapper;
        private readonly IEurolandIDProfileUnitOfWork _eurolandIDProfileUnitOfWork;
        private readonly ISharkUnitOfWork _sharkUnitOfWork;

        public UserService(IMapper mapper,
                           IEurolandIDProfileUnitOfWork eurolandIDProfileUnitOfWork,
                           ISharkUnitOfWork sharkUnitOfWork)
        {
            _mapper = mapper;
            _eurolandIDProfileUnitOfWork = eurolandIDProfileUnitOfWork;
            _sharkUnitOfWork = sharkUnitOfWork;
        }

        public async Task<bool> FollowCompany(string companyCode,
                                              Guid userId,
                                              UserCompanySubscriptionStatusEnum status)
        {
            var subscription = await _eurolandIDProfileUnitOfWork.UserCompanySubscriptionRepository.GetSubscription(userId, companyCode);
            if (subscription == default)
            {
                var company = await _sharkUnitOfWork.CompanySettingRepository.GetByCompanyCodeAsync(companyCode);
                if (company == default)
                {
                    throw new Exception("Not found company");
                }

                await _eurolandIDProfileUnitOfWork.UserCompanySubscriptionRepository.AddAsync(new UserCompanySubscriptionEntity
                {
                    Id = Guid.NewGuid(),
                    CompanyCode = companyCode,
                    Status = status,
                    LastModifyDate = DatetimeExts.DateTimeUtcNow(),
                    UserId = userId
                });
            }
            else
            {
                subscription.Status = status;
            }

            await _eurolandIDProfileUnitOfWork.SaveChangesAsync();

            return true;
        }

        public async Task<UserProfileOutputDto> GetProfileAsync(string companyCode, Guid userId)
        {
            var userExist = await _eurolandIDProfileUnitOfWork.UserRepository.GetByKeyAsync(userId);

            var userInfo = _mapper.Map<UserProfileOutputDto>(userExist);

            if (userExist == default)
            {
                var newUser = new UserEntity
                {
                    Id = Guid.Parse(TokenExts.GetUserId()),
                    Fullname = TokenExts.GetFullName(),
                    Email = TokenExts.GetEmail(),
                    UserLanguage = "en-GB"
                };

                await _eurolandIDProfileUnitOfWork.UserRepository.AddAsync(newUser);

                var ucs = new UserCompanySubscriptionEntity
                {
                    CompanyCode = companyCode,
                    LastModifyDate = DatetimeExts.DateTimeUtcNow(),
                    Status = UserCompanySubscriptionStatusEnum.Following,
                    UserId = newUser.Id
                };

                await _eurolandIDProfileUnitOfWork.UserCompanySubscriptionRepository.AddAsync(ucs);

                await _eurolandIDProfileUnitOfWork.SaveChangesAsync();

                return userInfo;
            }

            var subscription = await _eurolandIDProfileUnitOfWork.UserCompanySubscriptionRepository.GetSubscription(userExist.Id, companyCode);

            userInfo.CurrentFollowingStatus = subscription == default ? UserCompanySubscriptionStatusEnum.Unfollow : subscription.Status;

            return userInfo;
        }

        public async Task<List<FollowingCompany>> GetFollowingCompanies(Guid userId, string? language)
        {
            if (string.IsNullOrEmpty(language))
            {
                throw new ArgumentNullException(nameof(language));
            }

            var user = await _eurolandIDProfileUnitOfWork.UserRepository.GetByKeyAsync(userId)
                            ?? throw new InvalidDataException($"Not found user by id {userId}");

            var userCompanySubscriptions = await _eurolandIDProfileUnitOfWork.UserCompanySubscriptionRepository
                                                           .GetQueryableSubscriptionByUserId(user.Id, UserCompanySubscriptionStatusEnum.Following)
                                                           .ToListAsync();

            var companyCodes = userCompanySubscriptions.Select(x => x.CompanyCode).ToList();

            var cSettingLanguageQuery = _sharkUnitOfWork.CompanySettingLanguageRepository
                                                         .GetAllByCompanyCodes(companyCodes, language)
                                    ?? throw new InvalidDataException($"Cannot find any company name {string.Join(", ", companyCodes)}");

            var cSettingLangDir = await cSettingLanguageQuery.ToDictionaryAsync(s => s.CompanyCode.ToLower(), s => s);

            var companySettings = await _sharkUnitOfWork.CompanySettingRepository
                                                   .GetAllByCompanyCodes(companyCodes)
                                                   .ToDictionaryAsync(s => s.CompanyCode.ToLower(), s => s);

            var query = userCompanySubscriptions
                               .Select(s => new FollowingCompany
                               {
                                   CompanyCode = s.CompanyCode.ToLower(),
                                   CompanyName = cSettingLangDir.ContainsKey(s.CompanyCode.ToLower())
                                                    ? cSettingLangDir[s.CompanyCode.ToLower()].Name
                                                    : $"{s.CompanyCode}, {language}",
                                   Industry = companySettings.TryGetValue(s.CompanyCode.ToLower(), out CompanySettingEntity? cpnSettingIndustry) ? cpnSettingIndustry.Industry : string.Empty,
                                   Logo = companySettings.TryGetValue(s.CompanyCode.ToLower(), out CompanySettingEntity? cpnSettingLogo) ? cpnSettingLogo.CompanyLogo : string.Empty,
                                   Subscribed = s.LastModifyDate,
                                   HomePage = cSettingLangDir.TryGetValue(s.CompanyCode.ToLower(), out var cUri)
                                                    ? cUri.HomePage
                                                    : string.Empty
                               }).ToList();

            return query;
        }

        public async Task<UserEntity> UpdateProfile(Guid userId, UserProfileInputDto userProfile)
        {
            var user = await _eurolandIDProfileUnitOfWork.UserRepository.GetByKeyAsync(userId) ?? throw new InvalidDataException($"Not found user by {userId}");
            user.Avatar = userProfile.Avatar;
            user.Fullname = userProfile.FullName;
            user.IsReceiveBrowserNotifications = userProfile.NotificationPreferences.IsReceiveBrowserNotifications;
            user.IsReceiveEmailNotifications = userProfile.NotificationPreferences.IsReceiveEmail;

            await _eurolandIDProfileUnitOfWork.SaveChangesAsync();
            return user;
        }

        public async Task<bool> DeleteAccount(Guid userId)
        {
            var user = await _eurolandIDProfileUnitOfWork.UserRepository.GetByKeyAsync(userId) ?? throw new InvalidDataException($"Not found user by {userId}");
            _eurolandIDProfileUnitOfWork.UserRepository.Remove(user);
            return true;
        }

        public async Task<UPRProfileOutputDto> GetProfileAsync(Guid userId)
        {
            var userExist = await _eurolandIDProfileUnitOfWork.UserRepository.GetByKeyAsync(userId);

            var userInfo = _mapper.Map<UPRProfileOutputDto>(userExist);

            if (userExist == default)
            {
                var newUser = new UserEntity
                {
                    Id = Guid.Parse(TokenExts.GetUserId()!),
                    Fullname = TokenExts.GetFullName(),
                    Email = TokenExts.GetEmail(),
                    UserLanguage = "en-gb"
                };

                _eurolandIDProfileUnitOfWork.UserRepository.Add(newUser);
                await _eurolandIDProfileUnitOfWork.SaveChangesAsync();
                userInfo = _mapper.Map<UPRProfileOutputDto>(newUser);
                return userInfo;
            }

            return userInfo;
        }
    }
}
