﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<RootNamespace>Euroland.Identity.UserPreferences</RootNamespace>
    	<AssemblyName>Euroland.Identity.UserPreferences</AssemblyName>
		<AspNetCoreHostingModel>OutOfProcess</AspNetCoreHostingModel>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.24.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.14" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.14" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.14">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.14" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.14">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="Scrutor" Version="6.0.1" />
		<PackageReference Include="Serilog" Version="4.2.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.Syslog" Version="0.2.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
	</ItemGroup>

</Project>
