{"ConnectionStrings": {"EurolandIDProfileConnection": "Server=**********;Database=EurolandIDProfiles;uid=uShark;pwd=**********;Trusted_Connection=false; Application Name=UserPreferences; TrustServerCertificate=True;", "SharkDbConnection": "Server=**********;Database=shark;uid=uShark;pwd=**********;Trusted_Connection=false; Application Name=UserPreferences; TrustServerCertificate=True;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedOrigins": ["http://localhost:3005"], "Keycloak": {"BaseUrl": "https://dev.vn.euroland.com/auth", "Realm": "irservices", "ClientId": "user-preferences-webapp", "AdminClientId": "user-preferences-api", "AdminClientSecret": "FlUzAG10fw9h1YptbAyVUqNCnI9zrTVp"}}