{"ConnectionStrings": {"EurolandIDProfileConnection": "Server=**********;Database=EurolandIDProfiles;uid=uShark;pwd=**********;Trusted_Connection=false; Application Name=UserPreferences; TrustServerCertificate=True;", "SharkDbConnection": "Server=**********;Database=shark;uid=uShark;pwd=**********;Trusted_Connection=false; Application Name=UserPreferences; TrustServerCertificate=True;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Syslog"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "**********", "port": "514", "appName": "Tools.RealtimePriceFeed", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "Tools.RealtimePriceFeed"}}, "AllowedOrigins": ["http://localhost:3005"]}