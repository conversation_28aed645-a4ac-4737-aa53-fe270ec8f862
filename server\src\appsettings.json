{"ConnectionStrings": {"EurolandIDProfileConnection": "Server=**********;Database=EurolandIDProfiles;uid=uShark;pwd=**********;Trusted_Connection=false; Application Name=UserPreferences; TrustServerCertificate=True;", "SharkDbConnection": "Server=**********;Database=shark;uid=uShark;pwd=**********;Trusted_Connection=false; Application Name=UserPreferences; TrustServerCertificate=True;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"Using": ["Serilog.Sinks.Syslog", "Serilog.Sinks.File"], "MinimumLevel": "Warning", "WriteTo": [{"Name": "UdpSyslog", "Args": {"host": "**********", "port": "514", "appName": "Tools.UserPreferences", "restrictedToMinimumLevel": "Warning", "outputTemplate": "[{Timestamp:HH:mm:ss}] {Message:lj}{NewLine}{Exception}"}}], "Properties": {"Application": "Tools.UserPreferences"}}, "AllowedHosts": "*", "AllowedOrigins": ["http://localhost:3005", "https://************"], "AIConfig": {"OpenAI": {"Endpoint": "https://api.openai.com/v1/chat/completions", "OpenAIKey": "********************************************************************************************************************************************************************"}}, "Azure": {"TranslatorApi": {"Endpoint": "https://api.cognitive.microsofttranslator.com/", "SubscriptionKey": ""}, "BlobStorage": {"ConnectionString": "DefaultEndpointsProtocol=https;AccountName=eastdeveaeurolandid;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net", "ContainerName": "avatars"}}, "WiseApi": {"BaseUrl": "https://se-wise-api.euroland.com/api/v1/query"}, "SDataGraphQL": "https://dev.vn.euroland.com/tools/apigateway/graphql", "Authentication": {"Keycloak": {"RequireHttpsMetadata": true, "Authority": "https://dev.vn.euroland.com/auth/realms/irservices", "ValidIssuer": "https://dev.vn.euroland.com/auth/realms/irservices", "ValidAudience": "account", "MetadataAddress": "https://dev.vn.euroland.com/auth/realms/irservices/.well-known/openid-configuration", "ClockSkewSeconds": 0}}, "Keycloak": {"BaseUrl": "https://dev.vn.euroland.com/auth", "Realm": "irservices", "ClientId": "user-preferences-webapp", "AdminClientId": "user-preferences-api", "AdminClientSecret": "FlUzAG10fw9h1YptbAyVUqNCnI9zrTVp"}}